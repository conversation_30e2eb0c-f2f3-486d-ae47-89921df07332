# -*- coding: utf-8 -*-
"""
Lumerical FDTD 自动化仿真脚本
=========================

功能：
1) 读取 NbTiN 的 (wavelength[nm], n, k) 表（.xlsx/.csv）
2) 生成材料 CSV 并 materialimport("NbTiN_data")
3) 按老师手稿搭建 3D 结构（单位 nm → μm）：
   Air 300 / Au 80 / SiO2 150 / NbTiN 19 / SiO2 100 / Air 300，单胞 300×300 nm（x,y 周期；z 为 PML）
   顶部空气里放 80×80×20 nm 的 Au 小方块（避免重叠，上表面上方 5 nm）
4) 两次仿真：
   A) 仅光源（归一化）
   B) 带结构（结构化）
   自动用监视器功率 / 源功率计算 R/T，并保存 CSV
5) 控制台打印 1000 nm 和 1500 nm 的 R、T 数值

作者：[您的姓名]
日期：2025-09-22
版本：2.0
"""

import os
import sys
import csv
import random
import logging
from pathlib import Path
from typing import Tuple, List, Optional

# ====================== 配置参数 ======================
class SimulationConfig:
    """仿真配置类"""

    def __init__(self):
        # === 改这里：你的 NbTiN 光学数据文件路径（.xlsx 或 .csv 均可）===
        self.NK_FILE = r"C:\Users\<USER>\Desktop\NbTiN_nk.xlsx"

        # 结构参数（单位：μm）
        self.UNIT_CELL_X = 0.300  # 300 nm
        self.UNIT_CELL_Y = 0.300  # 300 nm

        # 层厚度（单位：μm）
        self.AIR_BOTTOM = 0.300
        self.AU_BOTTOM = 0.080
        self.SIO2_BOTTOM = 0.150
        self.NBTIN = 0.019
        self.SIO2_TOP = 0.100
        self.AIR_TOP = 0.300

        # Au 小方块参数（单位：μm）
        self.AU_PATCH_WIDTH = 0.080
        self.AU_PATCH_LENGTH = 0.080
        self.AU_PATCH_THICKNESS = 0.020
        self.AU_PATCH_GAP = 0.005  # 离上表面距离

        # 仿真参数
        self.WAVELENGTH_START = 0.80  # 800 nm
        self.WAVELENGTH_STOP = 2.00   # 2000 nm
        self.SIMULATION_TIME = 2000e-15  # fs
        self.MESH_ACCURACY = 3

        # 输出参数
        self.REPORT_WAVELENGTHS = [1.000, 1.500]  # μm

        # 日志配置
        self.LOG_LEVEL = logging.INFO

# 创建配置实例
config = SimulationConfig()

# ====================== 日志配置 ======================
def setup_logging():
    """设置日志配置"""
    logging.basicConfig(
        level=config.LOG_LEVEL,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(sys.stdout),
            logging.FileHandler('lumerical_simulation.log', encoding='utf-8')
        ]
    )
    return logging.getLogger(__name__)

logger = setup_logging()

# ====================== 工具函数 ======================
def ensure_pandas() -> bool:
    """
    尝试使用 pandas+openpyxl 读 xlsx；若无则尝试自动安装；失败则返回 False。

    Returns:
        bool: 是否成功导入 pandas 和 openpyxl
    """
    try:
        import pandas as pd  # noqa
        import openpyxl      # noqa
        return True
    except ImportError:
        try:
            import subprocess
            logger.info("Installing pandas & openpyxl into Lumerical Python ...")
            subprocess.check_call([sys.executable, "-m", "pip", "install", "-U", "pandas", "openpyxl"])
            import pandas as pd  # noqa
            import openpyxl      # noqa
            logger.info("pandas/openpyxl installed successfully.")
            return True
        except Exception as e:
            logger.warning(f"pandas/openpyxl not available: {e}")
            return False

def read_nk_table(nk_path: str) -> Tuple[List[float], List[float], List[float]]:
    """
    读取材料光学常数表 (λ[nm], n, k)，输出 λ[μm], n, k 的列表。
    支持 .xlsx / .csv 格式。

    Args:
        nk_path: 材料数据文件路径

    Returns:
        Tuple[List[float], List[float], List[float]]: (波长[μm], 折射率n, 消光系数k)

    Raises:
        FileNotFoundError: 文件不存在
        ValueError: 列名识别失败
        RuntimeError: 无法读取 xlsx 文件
    """
    nk_path = Path(nk_path).resolve()
    if not nk_path.exists():
        raise FileNotFoundError(f"材料数据文件不存在: {nk_path}")

    logger.info(f"读取材料数据文件: {nk_path}")

    ext = nk_path.suffix.lower()
    wl_um, n_val, k_val = [], [], []

    # 定义可能的列名
    wavelength_names = ["wavelength(nm)", "wavelength", "lambda(nm)", "λ(nm)", "nm", "wl"]
    n_names = ["n", "real(n)", "real", "n_real"]
    k_names = ["k", "imag(k)", "imag", "k_imag"]

    def find_column(column_names: List[str], available_columns: List[str]) -> Optional[str]:
        """在可用列中查找匹配的列名"""
        available_lower = [col.strip().lower() for col in available_columns]
        for name in column_names:
            if name in available_lower:
                return available_columns[available_lower.index(name)]
        return None

    if ext in [".xlsx", ".xls"]:
        if not ensure_pandas():
            raise RuntimeError(
                "无法读取 .xlsx 文件（pandas/openpyxl 不可用且安装失败）。\n"
                "请将该 xlsx 文件另存为 .csv 格式（列名包含 wavelength(nm), n, k），\n"
                f"然后修改配置中的 NK_FILE 路径为 .csv 文件路径。"
            )

        import pandas as pd
        try:
            df = pd.read_excel(nk_path)
            logger.info(f"成功读取 Excel 文件，共 {len(df)} 行数据")
        except Exception as e:
            raise RuntimeError(f"读取 Excel 文件失败: {e}")

        # 查找列名
        wl_key = find_column(wavelength_names, df.columns.tolist())
        n_key = find_column(n_names, df.columns.tolist())
        k_key = find_column(k_names, df.columns.tolist())

        if not all([wl_key, n_key, k_key]):
            raise ValueError(
                f"列名识别失败。\n"
                f"可用列名: {list(df.columns)}\n"
                f"需要包含: wavelength(nm), n, k 或类似名称"
            )

        logger.info(f"识别到列名 - 波长: {wl_key}, n: {n_key}, k: {k_key}")

        for _, row in df.iterrows():
            try:
                wl_um.append(float(row[wl_key]) / 1000.0)  # nm -> μm
                n_val.append(float(row[n_key]))
                k_val.append(float(row[k_key]))
            except (ValueError, TypeError) as e:
                logger.warning(f"跳过无效数据行: {row.to_dict()}, 错误: {e}")
                continue

    else:  # CSV 文件
        try:
            with open(nk_path, newline='', encoding="utf-8-sig") as f:
                reader = csv.DictReader(f)
                if not reader.fieldnames:
                    raise ValueError("CSV 文件没有列名")

                # 查找列名
                wl_key = find_column(wavelength_names, reader.fieldnames)
                n_key = find_column(n_names, reader.fieldnames)
                k_key = find_column(k_names, reader.fieldnames)

                if not all([wl_key, n_key, k_key]):
                    raise ValueError(
                        f"CSV 列名识别失败。\n"
                        f"可用列名: {reader.fieldnames}\n"
                        f"需要包含: wavelength(nm), n, k 或类似名称"
                    )

                logger.info(f"识别到列名 - 波长: {wl_key}, n: {n_key}, k: {k_key}")

                for i, row in enumerate(reader):
                    try:
                        wl_um.append(float(row[wl_key]) / 1000.0)  # nm -> μm
                        n_val.append(float(row[n_key]))
                        k_val.append(float(row[k_key]))
                    except (ValueError, TypeError) as e:
                        logger.warning(f"跳过第 {i+2} 行无效数据: {row}, 错误: {e}")
                        continue

        except Exception as e:
            raise RuntimeError(f"读取 CSV 文件失败: {e}")

    if not wl_um:
        raise ValueError("没有读取到有效的材料数据")

    logger.info(f"成功读取 {len(wl_um)} 个数据点")
    return wl_um, n_val, k_val

def write_material_csv(wl_um: List[float], n_val: List[float], k_val: List[float], out_csv: str) -> str:
    """
    将材料光学常数数据写入CSV文件

    Args:
        wl_um: 波长列表 [μm]
        n_val: 折射率列表
        k_val: 消光系数列表
        out_csv: 输出CSV文件路径

    Returns:
        str: 输出文件路径
    """
    try:
        with open(out_csv, "w", newline="", encoding="utf-8") as f:
            writer = csv.writer(f)
            writer.writerow(["wavelength", "n", "k"])
            for wl, n, k in zip(wl_um, n_val, k_val):
                writer.writerow([wl, n, k])
        logger.info(f"材料数据已写入: {out_csv} ({len(wl_um)} 个数据点)")
        return out_csv
    except Exception as e:
        raise RuntimeError(f"写入材料CSV文件失败: {e}")

# ====================== Lumerical API 导入 ======================
def setup_lumerical_api():
    """设置 Lumerical API 路径并导入 lumapi"""

    # 可能的 Lumerical 安装路径
    possible_paths = [
        r"C:\Program Files\Lumerical\FDTD\api\python",
        r"C:\Program Files\Lumerical\v232\api\python",
        r"C:\Program Files\Lumerical\v231\api\python",
        r"C:\Program Files\Lumerical\v230\api\python",
        r"C:\Program Files\AnsysEM\Lumerical\api\python",
        r"C:\Program Files (x86)\Lumerical\FDTD\api\python",
    ]

    # 添加找到的路径到 sys.path
    added_paths = []
    for path in possible_paths:
        if Path(path).exists() and path not in sys.path:
            sys.path.append(path)
            added_paths.append(path)

    if added_paths:
        logger.info(f"已添加 Lumerical API 路径: {added_paths}")
    else:
        logger.warning("未找到 Lumerical API 路径，请确保 Lumerical 已正确安装")

    # 尝试导入 lumapi
    try:
        import lumapi
        logger.info("成功导入 Lumerical API")
        return lumapi
    except ImportError as e:
        raise ImportError(
            f"无法导入 Lumerical API: {e}\n"
            f"请确保 Lumerical 已正确安装，或手动添加 API 路径到 Python 环境"
        )

# 导入 Lumerical API
lumapi = setup_lumerical_api()

# ====================== 仿真类 ======================
class LumericalSimulation:
    """Lumerical FDTD 仿真类"""

    def __init__(self, config: SimulationConfig):
        self.config = config
        self.fdtd = None
        self.material_data = None

    def initialize_fdtd(self):
        """初始化 FDTD 仿真环境"""
        try:
            self.fdtd = lumapi.FDTD(hide=False)
            self.fdtd.eval("switchtolayout;")
            self.fdtd.eval("deleteall;")
            self.fdtd.eval("setunits('length','microns');")
            self.fdtd.eval("setunits('time','femtoseconds');")
            self.fdtd.eval("redrawoff;")
            logger.info("FDTD 仿真环境初始化完成")
        except Exception as e:
            raise RuntimeError(f"FDTD 初始化失败: {e}")

    def load_material_data(self):
        """加载材料数据"""
        logger.info(f"加载 NbTiN 材料数据: {self.config.NK_FILE}")
        wl_um, n_tab, k_tab = read_nk_table(self.config.NK_FILE)

        # 保存材料数据到同目录
        out_dir = Path(self.config.NK_FILE).parent
        mat_csv = out_dir / "NbTiN_data.csv"
        write_material_csv(wl_um, n_tab, k_tab, str(mat_csv))

        self.material_data = {
            'wavelength': wl_um,
            'n': n_tab,
            'k': k_tab,
            'csv_path': mat_csv
        }

        return mat_csv

    def import_material(self, mat_csv_path: Path):
        """导入材料到 Lumerical 材料库"""
        if not self.fdtd:
            raise RuntimeError("FDTD 未初始化")

        # 转换路径格式（Windows 路径中的反斜杠转为正斜杠）
        mat_csv_script = str(mat_csv_path).replace("\\", "/")

        try:
            # 删除已存在的材料
            self.fdtd.eval('if (materialexists("NbTiN_data")) { deletematerial("NbTiN_data"); }')

            # 导入新材料
            self.fdtd.eval(f'materialimport("{mat_csv_script}","NbTiN_data");')
            logger.info("材料 'NbTiN_data' 导入成功")

        except Exception as e:
            raise RuntimeError(f"材料导入失败: {e}")

    def setup_fdtd_region(self):
        """设置 FDTD 仿真区域"""
        if not self.fdtd:
            raise RuntimeError("FDTD 未初始化")

        # 计算总厚度
        total_thickness = (self.config.AIR_BOTTOM + self.config.AU_BOTTOM +
                          self.config.SIO2_BOTTOM + self.config.NBTIN +
                          self.config.SIO2_TOP + self.config.AIR_TOP)

        try:
            # 添加 FDTD 仿真区域
            self.fdtd.eval("addfdtd;")
            self.fdtd.eval('set("name","FDTD");')
            self.fdtd.eval('set("dimension","3D");')
            self.fdtd.eval(f'set("x span",{self.config.UNIT_CELL_X});')
            self.fdtd.eval(f'set("y span",{self.config.UNIT_CELL_Y});')
            self.fdtd.eval(f'set("z span",{total_thickness});')
            self.fdtd.eval(f'set("mesh accuracy",{self.config.MESH_ACCURACY});')

            # 设置边界条件（x,y 周期；z=PML）
            self.fdtd.eval('set("x min bc","Periodic");')
            self.fdtd.eval('set("x max bc","Periodic");')
            self.fdtd.eval('set("y min bc","Periodic");')
            self.fdtd.eval('set("y max bc","Periodic");')
            self.fdtd.eval('set("z min bc","PML");')
            self.fdtd.eval('set("z max bc","PML");')

            # 设置波长范围
            self.fdtd.eval(f'set("wavelength start",{self.config.WAVELENGTH_START});')
            self.fdtd.eval(f'set("wavelength stop",{self.config.WAVELENGTH_STOP});')
            self.fdtd.eval(f'set("simulation time",{self.config.SIMULATION_TIME});')

            logger.info("FDTD 仿真区域设置完成")

        except Exception as e:
            raise RuntimeError(f"FDTD 区域设置失败: {e}")

    def add_layer(self, name: str, material: str, z_center: float, thickness: float):
        """添加矩形层结构"""
        if not self.fdtd:
            raise RuntimeError("FDTD 未初始化")

        try:
            self.fdtd.eval("addrect;")
            self.fdtd.eval(f'set("name","{name}");')
            self.fdtd.eval(f'set("material","{material}");')
            self.fdtd.eval(f'set("x span",{self.config.UNIT_CELL_X});')
            self.fdtd.eval(f'set("y span",{self.config.UNIT_CELL_Y});')
            self.fdtd.eval(f'set("z",{z_center});')
            self.fdtd.eval(f'set("z span",{thickness});')
            logger.debug(f"添加层结构: {name} ({material}), z={z_center}, t={thickness}")

        except Exception as e:
            raise RuntimeError(f"添加层结构失败 ({name}): {e}")

    def build_structure(self):
        """构建完整的多层结构"""
        if not self.fdtd:
            raise RuntimeError("FDTD 未初始化")

        logger.info("开始构建多层结构...")

        # 计算总厚度和起始位置
        total_thickness = (self.config.AIR_BOTTOM + self.config.AU_BOTTOM +
                          self.config.SIO2_BOTTOM + self.config.NBTIN +
                          self.config.SIO2_TOP + self.config.AIR_TOP)
        z0 = -0.5 * total_thickness

        try:
            # 自下而上构建层结构
            current_z = z0

            # 底部空气层
            z_center = current_z + self.config.AIR_BOTTOM * 0.5
            self.add_layer("air_bottom", "Air", z_center, self.config.AIR_BOTTOM)
            current_z += self.config.AIR_BOTTOM

            # 底部金层
            z_center = current_z + self.config.AU_BOTTOM * 0.5
            self.add_layer("Au_bottom", "Au (Gold) - Johnson and Christy", z_center, self.config.AU_BOTTOM)
            current_z += self.config.AU_BOTTOM

            # 底部二氧化硅层
            z_center = current_z + self.config.SIO2_BOTTOM * 0.5
            self.add_layer("SiO2_bottom", "SiO2 (Glass) - Palik", z_center, self.config.SIO2_BOTTOM)
            current_z += self.config.SIO2_BOTTOM

            # NbTiN 层
            z_center = current_z + self.config.NBTIN * 0.5
            self.add_layer("NbTiN", "NbTiN_data", z_center, self.config.NBTIN)
            current_z += self.config.NBTIN

            # 顶部二氧化硅层
            z_center = current_z + self.config.SIO2_TOP * 0.5
            self.add_layer("SiO2_top", "SiO2 (Glass) - Palik", z_center, self.config.SIO2_TOP)
            z_sio2_top = current_z + self.config.SIO2_TOP  # 记录顶部位置
            current_z += self.config.SIO2_TOP

            # 顶部空气层
            z_center = current_z + self.config.AIR_TOP * 0.5
            self.add_layer("air_top", "Air", z_center, self.config.AIR_TOP)

            # 添加顶部空气中的 Au 小方块
            self._add_au_patch(z_sio2_top)

            logger.info("多层结构构建完成")

        except Exception as e:
            raise RuntimeError(f"结构构建失败: {e}")

    def _add_au_patch(self, z_sio2_top: float):
        """在顶部空气中添加 Au 小方块"""
        # Au 小方块位置（离上表面一定距离，避免几何重叠）
        z_center = z_sio2_top + self.config.AU_PATCH_THICKNESS * 0.5 + self.config.AU_PATCH_GAP

        # 计算随机位置（确保不超出单胞边界）
        max_offset_x = 0.5 * self.config.UNIT_CELL_X - 0.5 * self.config.AU_PATCH_WIDTH
        max_offset_y = 0.5 * self.config.UNIT_CELL_Y - 0.5 * self.config.AU_PATCH_LENGTH

        x_center = 0.0 if max_offset_x <= 0 else random.uniform(-max_offset_x, max_offset_x)
        y_center = 0.0 if max_offset_y <= 0 else random.uniform(-max_offset_y, max_offset_y)

        try:
            self.fdtd.eval("addrect;")
            self.fdtd.eval('set("name","Au_patch");')
            self.fdtd.eval('set("material","Au (Gold) - Johnson and Christy");')
            self.fdtd.eval(f'set("x",{x_center});')
            self.fdtd.eval(f'set("x span",{self.config.AU_PATCH_WIDTH});')
            self.fdtd.eval(f'set("y",{y_center});')
            self.fdtd.eval(f'set("y span",{self.config.AU_PATCH_LENGTH});')
            self.fdtd.eval(f'set("z",{z_center});')
            self.fdtd.eval(f'set("z span",{self.config.AU_PATCH_THICKNESS});')

            logger.info(f"Au 小方块添加完成: 位置({x_center:.3f}, {y_center:.3f}, {z_center:.3f})")

        except Exception as e:
            raise RuntimeError(f"Au 小方块添加失败: {e}")

    def clear_structure(self):
        """清除所有结构对象"""
        if not self.fdtd:
            return

        structure_names = ["Au_patch", "SiO2_top", "NbTiN", "SiO2_bottom", "Au_bottom", "air_top", "air_bottom"]

        for name in structure_names:
            try:
                self.fdtd.eval(f'if (find("{name}")) {{ select("{name}"); delete; }}')
            except Exception as e:
                logger.warning(f"删除结构 {name} 时出错: {e}")

        logger.info("结构清除完成")

    def add_source_and_monitors(self):
        """添加光源和功率监视器"""
        if not self.fdtd:
            raise RuntimeError("FDTD 未初始化")

        logger.info("添加光源和监视器...")

        # 计算关键位置
        total_thickness = (self.config.AIR_BOTTOM + self.config.AU_BOTTOM +
                          self.config.SIO2_BOTTOM + self.config.NBTIN +
                          self.config.SIO2_TOP + self.config.AIR_TOP)
        z0 = -0.5 * total_thickness
        z_sio2_top = z0 + self.config.AIR_BOTTOM + self.config.AU_BOTTOM + self.config.SIO2_BOTTOM + self.config.NBTIN + self.config.SIO2_TOP

        # 光源位置（顶部空气中）
        src_z = z0 + total_thickness * 0.5 - 0.1 * self.config.AIR_TOP

        # 监视器位置
        refl_z = z_sio2_top + 0.10 * self.config.AIR_TOP    # 反射监视器（顶部空气内）
        trans_z = z0 + 0.10 * self.config.AIR_BOTTOM        # 透射监视器（底部空气内）

        try:
            # 添加平面波光源
            self.fdtd.eval("addplane;")
            self.fdtd.eval('set("name","src");')
            self.fdtd.eval('set("injection axis","z");')
            self.fdtd.eval('set("direction","Backward");')  # 向下传播
            self.fdtd.eval(f'set("x span",{self.config.UNIT_CELL_X});')
            self.fdtd.eval(f'set("y span",{self.config.UNIT_CELL_Y});')
            self.fdtd.eval(f'set("z",{src_z});')
            self.fdtd.eval(f'set("wavelength start",{self.config.WAVELENGTH_START});')
            self.fdtd.eval(f'set("wavelength stop",{self.config.WAVELENGTH_STOP});')

            # 添加反射功率监视器
            self.fdtd.eval("addpower;")
            self.fdtd.eval('set("name","R_monitor");')
            self.fdtd.eval('set("monitor type","2D Z-normal");')
            self.fdtd.eval(f'set("x span",{self.config.UNIT_CELL_X});')
            self.fdtd.eval(f'set("y span",{self.config.UNIT_CELL_Y});')
            self.fdtd.eval(f'set("z",{refl_z});')
            self.fdtd.eval('set("override global monitor settings",1);')
            self.fdtd.eval('set("use source limits",1);')

            # 添加透射功率监视器
            self.fdtd.eval("addpower;")
            self.fdtd.eval('set("name","T_monitor");')
            self.fdtd.eval('set("monitor type","2D Z-normal");')
            self.fdtd.eval(f'set("x span",{self.config.UNIT_CELL_X});')
            self.fdtd.eval(f'set("y span",{self.config.UNIT_CELL_Y});')
            self.fdtd.eval(f'set("z",{trans_z});')
            self.fdtd.eval('set("override global monitor settings",1);')
            self.fdtd.eval('set("use source limits",1);')

            logger.info(f"光源和监视器添加完成 - 光源位置: {src_z:.3f}, 反射监视器: {refl_z:.3f}, 透射监视器: {trans_z:.3f}")

        except Exception as e:
            raise RuntimeError(f"光源和监视器添加失败: {e}")

    def get_monitor_data(self, monitor_name: str) -> Tuple[List[float], List[float]]:
        """
        从监视器获取数据

        Args:
            monitor_name: 监视器名称

        Returns:
            Tuple[List[float], List[float]]: (波长, 功率分数)
        """
        if not self.fdtd:
            raise RuntimeError("FDTD 未初始化")

        try:
            # 优先尝试读取归一化透射率/反射率
            try:
                result = self.fdtd.getresult(monitor_name, "T")
                wavelength = result["lambda"]
                fraction = result["T"]
                logger.debug(f"从 {monitor_name} 读取归一化数据")
                return wavelength, fraction
            except Exception:
                # 如果归一化数据不可用，则手动计算
                power_result = self.fdtd.getresult(monitor_name, "power")
                wavelength = power_result["lambda"]
                monitor_power = power_result["power"]

                # 获取源功率
                self.fdtd.eval("sp = sourcepower('src');")
                source_power = self.fdtd.getv("sp")

                # 计算功率分数
                fraction = monitor_power / source_power
                logger.debug(f"从 {monitor_name} 计算功率分数")
                return wavelength, fraction

        except Exception as e:
            raise RuntimeError(f"从监视器 {monitor_name} 获取数据失败: {e}")

    def interpolate_at_wavelength(self, wavelength: List[float], values: List[float], target_wavelength: float) -> float:
        """在指定波长处插值"""
        import numpy as np

        wavelength = np.array(wavelength)
        values = np.array(values)

        if target_wavelength <= wavelength.min():
            return float(values[0])
        if target_wavelength >= wavelength.max():
            return float(values[-1])

        return float(np.interp(target_wavelength, wavelength, values))

    def run_simulation(self) -> Tuple[List[float], List[float], List[float], List[float]]:
        """
        运行仿真并返回结果

        Returns:
            Tuple: (反射波长, 反射率, 透射波长, 透射率)
        """
        if not self.fdtd:
            raise RuntimeError("FDTD 未初始化")

        try:
            logger.info("开始运行 FDTD 仿真...")
            self.fdtd.eval("run;")
            logger.info("仿真完成，正在提取数据...")

            # 获取反射和透射数据
            lam_R, R_data = self.get_monitor_data("R_monitor")
            lam_T, T_data = self.get_monitor_data("T_monitor")

            logger.info(f"数据提取完成 - 反射数据点: {len(lam_R)}, 透射数据点: {len(lam_T)}")
            return lam_R, R_data, lam_T, T_data

        except Exception as e:
            raise RuntimeError(f"仿真运行失败: {e}")

    def save_results(self, wavelength_R: List[float], reflectance: List[float],
                    wavelength_T: List[float], transmittance: List[float],
                    filename: str):
        """保存仿真结果到CSV文件"""
        try:
            output_path = Path(self.config.NK_FILE).parent / filename

            with open(output_path, "w", newline="", encoding="utf-8") as f:
                writer = csv.writer(f)
                writer.writerow(["lambda_um", "R", "T"])

                # 假设反射和透射的波长数组相同，如果不同需要插值对齐
                for i, wl in enumerate(wavelength_R):
                    r_val = max(0.0, float(reflectance[i]))
                    t_val = max(0.0, float(transmittance[i])) if i < len(transmittance) else 0.0
                    writer.writerow([float(wl), r_val, t_val])

            logger.info(f"结果已保存到: {output_path}")
            return output_path

        except Exception as e:
            raise RuntimeError(f"保存结果失败: {e}")

    def finalize(self):
        """完成仿真，恢复界面"""
        if self.fdtd:
            try:
                self.fdtd.eval("redrawon;")
                logger.info("仿真界面已恢复")
            except Exception as e:
                logger.warning(f"恢复界面时出错: {e}")

# ====================== 主执行函数 ======================
def main():
    """主执行函数"""
    try:
        logger.info("开始 Lumerical FDTD 仿真...")

        # 创建仿真实例
        simulation = LumericalSimulation(config)

        # 初始化 FDTD 环境
        simulation.initialize_fdtd()

        # 加载材料数据
        mat_csv_path = simulation.load_material_data()

        # 导入材料
        simulation.import_material(mat_csv_path)

        # 设置 FDTD 仿真区域
        simulation.setup_fdtd_region()

        # 添加光源和监视器
        simulation.add_source_and_monitors()

        # === A) 仅光源仿真（归一化基准）===
        logger.info("开始基准仿真（仅光源）...")
        simulation.clear_structure()  # 确保没有结构
        lam_R_ref, R_ref, lam_T_ref, T_ref = simulation.run_simulation()

        # 保存基准数据
        ref_path = simulation.save_results(lam_R_ref, R_ref, lam_T_ref, T_ref, "RT_reference.csv")
        logger.info(f"基准数据已保存: {ref_path}")

        # === B) 带结构仿真 ===
        logger.info("开始结构仿真...")
        simulation.build_structure()
        lam_R, R_data, lam_T, T_data = simulation.run_simulation()

        # 保存结构仿真数据
        struct_path = simulation.save_results(lam_R, R_data, lam_T, T_data, "RT_structure.csv")
        logger.info(f"结构仿真数据已保存: {struct_path}")

        # 打印关键波长的 R/T 值
        logger.info("关键波长的反射率和透射率:")
        print("\n" + "="*60)
        print("关键波长的仿真结果:")
        print("="*60)

        for target_wl in config.REPORT_WAVELENGTHS:
            r_val = simulation.interpolate_at_wavelength(lam_R, R_data, target_wl)
            t_val = simulation.interpolate_at_wavelength(lam_T, T_data, target_wl)
            r_val = max(0.0, r_val)
            t_val = max(0.0, t_val)

            print(f"λ = {target_wl*1000:.0f} nm:  R = {r_val:.4f},  T = {t_val:.4f},  (R+T = {r_val+t_val:.4f})")
            logger.info(f"λ={target_wl*1000:.0f} nm: R={r_val:.4f}, T={t_val:.4f}")

        print("="*60)

        # 完成仿真
        simulation.finalize()

        logger.info("所有仿真完成！")
        print(f"\n[完成] 归一化 + 结构化 两段仿真全部完成")
        print(f"基准数据: {ref_path}")
        print(f"结构数据: {struct_path}")
        print(f"关键波长 R/T 已打印")

    except Exception as e:
        logger.error(f"仿真过程中发生错误: {e}")
        print(f"\n[错误] 仿真失败: {e}")
        raise

if __name__ == "__main__":
    main()
