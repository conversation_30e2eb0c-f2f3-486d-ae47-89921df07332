# 最终可用版本 - 使用正确的材料名称和监视器

# 参数设置
px = 300e-9;
py = 300e-9;
air_bot = 300e-9;
t_au_bot = 80e-9;
t_sio2_bot = 150e-9;
t_nbtiin = 19e-9;
t_sio2_top = 100e-9;
air_top = 300e-9;
patch_w = 80e-9;
patch_l = 80e-9;
patch_t = 20e-9;
patch_gap = 5e-9;

# 初始化
?"开始仿真...";
switchtolayout;
deleteall;
redrawoff;

# 计算总厚度
lz = air_bot + t_au_bot + t_sio2_bot + t_nbtiin + t_sio2_top + air_top;
z0 = -lz/2;

# 添加 FDTD 区域
addfdtd;
set("dimension", "3D");
set("x span", px);
set("y span", py);
set("z span", lz);
set("mesh accuracy", 3);
set("x min bc", "Periodic");
set("x max bc", "Periodic");
set("y min bc", "Periodic");
set("y max bc", "Periodic");
set("z min bc", "PML");
set("z max bc", "PML");

?"FDTD 区域设置完成";

# 添加光源
addplane;
set("injection axis", "z");
set("direction", "Backward");
set("x span", px);
set("y span", py);
set("z", z0 + lz*0.8);

?"光源添加完成";

# 添加监视器 - 使用新的函数名
z_sio2_top = z0 + air_bot + t_au_bot + t_sio2_bot + t_nbtiin + t_sio2_top;

# 反射监视器
adddftmonitor;
set("monitor type", "2D Z-normal");
set("x span", px);
set("y span", py);
set("z", z_sio2_top + 0.05*air_top);

# 透射监视器
adddftmonitor;
set("monitor type", "2D Z-normal");
set("x span", px);
set("y span", py);
set("z", z0 + 0.05*air_bot);

?"监视器添加完成";

# 第一次仿真：仅光源
?"运行基准仿真...";
run;
?"基准仿真完成";

# 构建结构
?"构建结构...";

current_z = z0;

# 底部空气层 - 实际上不需要添加，因为背景就是空气
current_z = current_z + air_bot;

# 底部金层
z_center = current_z + t_au_bot * 0.5;
addrect;
set("material", "Au (Gold) - Johnson and Christy");
set("x span", px);
set("y span", py);
set("z", z_center);
set("z span", t_au_bot);
current_z = current_z + t_au_bot;

# 底部二氧化硅层
z_center = current_z + t_sio2_bot * 0.5;
addrect;
set("material", "SiO2 (Glass) - Palik");
set("x span", px);
set("y span", py);
set("z", z_center);
set("z span", t_sio2_bot);
current_z = current_z + t_sio2_bot;

# NbTiN 层 - 使用 TiN 材料（更接近 NbTiN）
z_center = current_z + t_nbtiin * 0.5;
addrect;
set("material", "TiN - Palik");
set("x span", px);
set("y span", py);
set("z", z_center);
set("z span", t_nbtiin);
current_z = current_z + t_nbtiin;

# 顶部二氧化硅层
z_center = current_z + t_sio2_top * 0.5;
addrect;
set("material", "SiO2 (Glass) - Palik");
set("x span", px);
set("y span", py);
set("z", z_center);
set("z span", t_sio2_top);
z_sio2_top_surface = current_z + t_sio2_top;
current_z = current_z + t_sio2_top;

# 顶部空气层 - 不需要添加，背景就是空气

# Au 小方块
patch_z = z_sio2_top_surface + patch_t*0.5 + patch_gap;
addrect;
set("material", "Au (Gold) - Johnson and Christy");
set("x", 0);
set("x span", patch_w);
set("y", 0);
set("y span", patch_l);
set("z", patch_z);
set("z span", patch_t);

?"结构构建完成";

# 第二次仿真：带结构
?"运行结构仿真...";
run;

?"仿真完成！";

# 尝试获取结果
?"尝试获取仿真结果...";

# 获取监视器结果
try {
    R_result = getresult("DFT", "power");
    ?"反射监视器数据获取成功";
    R_power = R_result.power;
    wavelength = R_result.lambda;
    
    # 计算 1000 nm 和 1500 nm 处的值
    lambda_1000 = 1000e-9;
    lambda_1500 = 1500e-9;
    
    R_1000 = interp(wavelength, R_power, lambda_1000);
    R_1500 = interp(wavelength, R_power, lambda_1500);
    
    ?"========================================";
    ?"仿真结果:";
    ?"========================================";
    ?"λ = 1000 nm: 反射功率 = " + num2str(R_1000, "%.4f");
    ?"λ = 1500 nm: 反射功率 = " + num2str(R_1500, "%.4f");
    ?"========================================";
    
} catch (error_msg) {
    ?"监视器数据获取失败，请手动查看结果";
}

redrawon;

?"========================================";
?"仿真完成！结构信息:";
?"========================================";
?"1. 底部金层 (80 nm) - Au (Gold) - Johnson and Christy";
?"2. 底部 SiO2 层 (150 nm) - SiO2 (Glass) - Palik";
?"3. NbTiN 层 (19 nm) - 使用 TiN - Palik 替代";
?"4. 顶部 SiO2 层 (100 nm) - SiO2 (Glass) - Palik";
?"5. Au 小方块 (80x80x20 nm) - Au (Gold) - Johnson and Christy";
?"========================================";
?"请在 3D 视图中查看结构";
?"监视器结果可在 Visualizer 中查看";
?"========================================";
