# -*- coding: utf-8 -*-
"""
Lumerical FDTD 仿真配置示例
========================

这个文件展示了如何自定义仿真参数。
复制这个文件并根据您的需求修改参数。
"""

import logging

class CustomSimulationConfig:
    """自定义仿真配置类"""
    
    def __init__(self):
        # ====================== 文件路径 ======================
        # 材料数据文件路径（支持 .xlsx 和 .csv 格式）
        self.NK_FILE = r"C:\Users\<USER>\Desktop\NbTiN_nk.xlsx"
        
        # ====================== 结构参数 ======================
        # 单胞尺寸（单位：μm）
        self.UNIT_CELL_X = 0.300  # 300 nm
        self.UNIT_CELL_Y = 0.300  # 300 nm
        
        # 层厚度（单位：μm，从下到上）
        self.AIR_BOTTOM = 0.300    # 底部空气层厚度
        self.AU_BOTTOM = 0.080     # 底部金层厚度
        self.SIO2_BOTTOM = 0.150   # 底部二氧化硅层厚度
        self.NBTIN = 0.019         # NbTiN 层厚度
        self.SIO2_TOP = 0.100      # 顶部二氧化硅层厚度
        self.AIR_TOP = 0.300       # 顶部空气层厚度
        
        # Au 小方块参数（位于顶部空气层中）
        self.AU_PATCH_WIDTH = 0.080      # 宽度（μm）
        self.AU_PATCH_LENGTH = 0.080     # 长度（μm）
        self.AU_PATCH_THICKNESS = 0.020  # 厚度（μm）
        self.AU_PATCH_GAP = 0.005        # 离上表面距离（μm）
        
        # ====================== 仿真参数 ======================
        # 波长范围
        self.WAVELENGTH_START = 0.80  # 起始波长（μm）800 nm
        self.WAVELENGTH_STOP = 2.00   # 结束波长（μm）2000 nm
        
        # 仿真设置
        self.SIMULATION_TIME = 2000e-15  # 仿真时间（fs）
        self.MESH_ACCURACY = 3           # 网格精度（1-8，数值越大越精确）
        
        # ====================== 输出参数 ======================
        # 需要特别报告的波长点（μm）
        self.REPORT_WAVELENGTHS = [1.000, 1.500]  # 1000 nm 和 1500 nm
        
        # 日志级别
        self.LOG_LEVEL = logging.INFO  # DEBUG, INFO, WARNING, ERROR

# ====================== 其他配置示例 ======================

class HighResolutionConfig(CustomSimulationConfig):
    """高分辨率仿真配置"""
    
    def __init__(self):
        super().__init__()
        # 提高网格精度
        self.MESH_ACCURACY = 5
        # 增加仿真时间
        self.SIMULATION_TIME = 3000e-15
        # 更详细的日志
        self.LOG_LEVEL = logging.DEBUG

class QuickTestConfig(CustomSimulationConfig):
    """快速测试配置"""
    
    def __init__(self):
        super().__init__()
        # 降低网格精度以加快仿真
        self.MESH_ACCURACY = 2
        # 减少仿真时间
        self.SIMULATION_TIME = 1000e-15
        # 缩小波长范围
        self.WAVELENGTH_START = 1.0
        self.WAVELENGTH_STOP = 1.6

class WideSpectrumConfig(CustomSimulationConfig):
    """宽光谱仿真配置"""
    
    def __init__(self):
        super().__init__()
        # 扩大波长范围
        self.WAVELENGTH_START = 0.4   # 400 nm
        self.WAVELENGTH_STOP = 3.0    # 3000 nm
        # 更多报告波长点
        self.REPORT_WAVELENGTHS = [0.5, 0.8, 1.0, 1.3, 1.5, 2.0, 2.5]

# ====================== 使用方法 ======================
"""
要使用自定义配置，请在主脚本中替换配置：

# 在 20250922lumerical.py 中找到这一行：
config = SimulationConfig()

# 替换为：
config = CustomSimulationConfig()
# 或者
config = HighResolutionConfig()
# 或者
config = QuickTestConfig()
"""

# ====================== 材料数据格式说明 ======================
"""
支持的材料数据文件格式：

1. Excel 文件 (.xlsx, .xls)
   - 波长列名：wavelength(nm), wavelength, lambda(nm), λ(nm), nm, wl
   - 折射率列名：n, real(n), real, n_real
   - 消光系数列名：k, imag(k), imag, k_imag

2. CSV 文件 (.csv)
   示例格式：
   wavelength(nm),n,k
   400,2.1,0.5
   450,2.0,0.4
   500,1.9,0.3
   ...

注意：
- 波长单位必须是纳米 (nm)
- 脚本会自动转换为微米 (μm)
- 数据点越多，插值精度越高
"""

# ====================== 结构修改示例 ======================
"""
如果需要修改结构，可以继承配置类：

class ThinnerNbTiNConfig(CustomSimulationConfig):
    def __init__(self):
        super().__init__()
        self.NBTIN = 0.010  # 将 NbTiN 层厚度改为 10 nm

class LargerPatchConfig(CustomSimulationConfig):
    def __init__(self):
        super().__init__()
        self.AU_PATCH_WIDTH = 0.120   # 120 nm
        self.AU_PATCH_LENGTH = 0.120  # 120 nm
        self.AU_PATCH_THICKNESS = 0.030  # 30 nm

class DifferentUnitCellConfig(CustomSimulationConfig):
    def __init__(self):
        super().__init__()
        self.UNIT_CELL_X = 0.400  # 400 nm
        self.UNIT_CELL_Y = 0.400  # 400 nm
"""
