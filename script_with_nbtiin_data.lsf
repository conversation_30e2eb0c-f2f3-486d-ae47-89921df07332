# 使用真实 NbTiN 数据的 Lumerical FDTD 脚本

# 参数设置
px = 300e-9;
py = 300e-9;
air_bot = 300e-9;
t_au_bot = 80e-9;
t_sio2_bot = 150e-9;
t_nbtiin = 19e-9;
t_sio2_top = 100e-9;
air_top = 300e-9;
patch_w = 80e-9;
patch_l = 80e-9;
patch_t = 20e-9;
patch_gap = 5e-9;

# NbTiN 数据文件路径
nbtiin_file = "C:\Users\<USER>\Desktop\NbTiN_nk.csv";

# 初始化
?"开始仿真...";
switchtolayout;
deleteall;
redrawoff;

# ====================== 创建 NbTiN 材料 ======================
?"正在读取 NbTiN 数据文件...";

# 检查文件是否存在
if (!fileexists(nbtiin_file)) {
    ?"错误: 找不到 NbTiN 数据文件: " + nbtiin_file;
    ?"请检查文件路径是否正确";
    break;
}

# 读取 CSV 文件
nbtiin_data = readdata(nbtiin_file);

# 提取数据列
wavelength_nm = nbtiin_data(:,1);  # 波长 (nm)
n_data = nbtiin_data(:,2);         # 折射率实部
k_data = nbtiin_data(:,3);         # 折射率虚部

# 转换波长单位从 nm 到 m
wavelength_m = wavelength_nm * 1e-9;

?"NbTiN 数据读取完成，数据点数: " + num2str(length(wavelength_nm));
?"波长范围: " + num2str(min(wavelength_nm)) + " - " + num2str(max(wavelength_nm)) + " nm";

# 创建 NbTiN 材料
addmaterial("Sampled data");
set("name", "NbTiN_data");

# 设置采样数据
set("sampled data", [wavelength_m, n_data, k_data]);

?"NbTiN 材料创建完成";

# ====================== 设置 FDTD 区域 ======================
?"设置 FDTD 区域...";

# 计算总厚度
lz = air_bot + t_au_bot + t_sio2_bot + t_nbtiin + t_sio2_top + air_top;
z0 = -lz/2;

# 添加 FDTD 区域
addfdtd;
set("dimension", "3D");
set("x span", px);
set("y span", py);
set("z span", lz);
set("mesh accuracy", 3);
set("x min bc", "Periodic");
set("x max bc", "Periodic");
set("y min bc", "Periodic");
set("y max bc", "Periodic");
set("z min bc", "PML");
set("z max bc", "PML");

?"FDTD 区域设置完成";

# ====================== 添加光源和监视器 ======================
?"添加光源和监视器...";

# 添加光源
addplane;
set("injection axis", "z");
set("direction", "Backward");
set("x span", px);
set("y span", py);
set("z", z0 + lz*0.8);

# 计算关键位置
z_sio2_top = z0 + air_bot + t_au_bot + t_sio2_bot + t_nbtiin + t_sio2_top;

# 反射监视器
adddftmonitor;
set("monitor type", "2D Z-normal");
set("x span", px);
set("y span", py);
set("z", z_sio2_top + 0.05*air_top);

# 透射监视器
adddftmonitor;
set("monitor type", "2D Z-normal");
set("x span", px);
set("y span", py);
set("z", z0 + 0.05*air_bot);

?"光源和监视器添加完成";

# ====================== 构建结构 ======================
?"构建结构...";

current_z = z0;

# 底部空气层 - 跳过，背景就是空气
current_z = current_z + air_bot;

# 底部金层
z_center = current_z + t_au_bot * 0.5;
addrect;
set("material", "Au (Gold) - Johnson and Christy");
set("x span", px);
set("y span", py);
set("z", z_center);
set("z span", t_au_bot);
current_z = current_z + t_au_bot;

# 底部二氧化硅层
z_center = current_z + t_sio2_bot * 0.5;
addrect;
set("material", "SiO2 (Glass) - Palik");
set("x span", px);
set("y span", py);
set("z", z_center);
set("z span", t_sio2_bot);
current_z = current_z + t_sio2_bot;

# NbTiN 层 - 使用真实数据
z_center = current_z + t_nbtiin * 0.5;
addrect;
set("material", "NbTiN_data");
set("x span", px);
set("y span", py);
set("z", z_center);
set("z span", t_nbtiin);
current_z = current_z + t_nbtiin;

# 顶部二氧化硅层
z_center = current_z + t_sio2_top * 0.5;
addrect;
set("material", "SiO2 (Glass) - Palik");
set("x span", px);
set("y span", py);
set("z", z_center);
set("z span", t_sio2_top);
z_sio2_top_surface = current_z + t_sio2_top;
current_z = current_z + t_sio2_top;

# Au 小方块
patch_z = z_sio2_top_surface + patch_t*0.5 + patch_gap;
addrect;
set("material", "Au (Gold) - Johnson and Christy");
set("x", 0);
set("x span", patch_w);
set("y", 0);
set("y span", patch_l);
set("z", patch_z);
set("z span", patch_t);

?"结构构建完成";

# ====================== 运行仿真 ======================
?"开始运行仿真...";
run;

?"仿真完成！";
redrawon;

# ====================== 输出结果 ======================
?"========================================";
?"仿真完成！结构信息:";
?"========================================";
?"1. 底部金层 (80 nm) - Au (Gold) - Johnson and Christy";
?"2. 底部 SiO2 层 (150 nm) - SiO2 (Glass) - Palik";
?"3. NbTiN 层 (19 nm) - 使用真实光学数据";
?"   数据点数: " + num2str(length(wavelength_nm));
?"   波长范围: " + num2str(min(wavelength_nm)) + " - " + num2str(max(wavelength_nm)) + " nm";
?"4. 顶部 SiO2 层 (100 nm) - SiO2 (Glass) - Palik";
?"5. Au 小方块 (80x80x20 nm) - Au (Gold) - Johnson and Christy";
?"========================================";
?"请在 3D 视图中查看结构";
?"监视器结果可在 Visualizer 中查看";
?"========================================";

?"提示: 如需查看 R/T 结果，请在 Visualizer 中查看 DFT 监视器数据";
