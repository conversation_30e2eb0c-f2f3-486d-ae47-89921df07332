# Lumerical FDTD 内部脚本版本
# 直接在 Lumerical Script Editor 中运行
# 
# 功能：
# 1) 读取 NbTiN 的 (wavelength[nm], n, k) 表并导入材料
# 2) 搭建 3D 结构：Air 300 / Au 80 / SiO2 150 / NbTiN 19 / SiO2 100 / Air 300
# 3) 顶部空气里放 80×80×20 nm 的 Au 小方块
# 4) 两次仿真：A) 仅光源（归一化）B) 带结构（结构化）
# 5) 计算并保存 R/T，打印关键波长数值

# ====================== 配置参数 ======================
# 改这里：你的 NbTiN 光学数据文件路径
NK_FILE = "C:/Users/<USER>/Desktop/NbTiN_nk.csv";  # 注意：建议使用 CSV 格式

# 结构参数（单位：米，Lumerical 默认单位）
px = 300e-9;         # 单胞 x 尺寸 (300 nm)
py = 300e-9;         # 单胞 y 尺寸 (300 nm)

# 层厚度（单位：米）
air_bot = 300e-9;    # 底部空气层 (300 nm)
t_au_bot = 80e-9;    # 底部金层 (80 nm)
t_sio2_bot = 150e-9; # 底部二氧化硅层 (150 nm)
t_nbtiin = 19e-9;    # NbTiN 层 (19 nm)
t_sio2_top = 100e-9; # 顶部二氧化硅层 (100 nm)
air_top = 300e-9;    # 顶部空气层 (300 nm)

# Au 小方块参数（单位：米）
patch_w = 80e-9;     # 宽度 (80 nm)
patch_l = 80e-9;     # 长度 (80 nm)
patch_t = 20e-9;     # 厚度 (20 nm)
patch_gap = 5e-9;    # 离上表面距离 (5 nm)

# 仿真参数
lam_start = 800e-9;  # 起始波长 (800 nm)
lam_stop = 2000e-9;  # 结束波长 (2000 nm)
sim_time = 2000e-15; # 仿真时间 (fs)
mesh_acc = 3;        # 网格精度

# 报告波长（米）
report_wavelengths = [1000e-9, 1500e-9];  # 1000 nm, 1500 nm

# ====================== 初始化 ======================
?"[INFO] 开始 Lumerical FDTD 仿真...";
switchtolayout;
deleteall;
redrawoff;

# ====================== 材料导入 ======================
?"[STEP] 导入 NbTiN 材料数据...";

# 检查材料文件是否存在
if (!fileexists(NK_FILE)) {
    ?"[ERROR] 材料文件不存在: " + NK_FILE;
    ?"请检查文件路径，或创建包含 wavelength(nm), n, k 列的 CSV 文件";
    break;
}

# 删除已存在的材料
if (materialexists("NbTiN_data")) {
    deletematerial("NbTiN_data");
}

# 导入材料
try {
    materialimport(NK_FILE, "NbTiN_data");
    ?"[INFO] 材料 'NbTiN_data' 导入成功";
} catch (error_msg) {
    ?"[ERROR] 材料导入失败: " + error_msg;
    ?"请确保 CSV 文件格式正确：wavelength(nm), n, k";
    break;
}

# ====================== 设置 FDTD 区域 ======================
?"[STEP] 设置 FDTD 仿真区域...";

# 计算总厚度
lz = air_bot + t_au_bot + t_sio2_bot + t_nbtiin + t_sio2_top + air_top;
z0 = -0.5 * lz;

# 添加 FDTD 区域
addfdtd;
set("name", "FDTD");
set("dimension", "3D");
set("x span", px);
set("y span", py);
set("z span", lz);
set("mesh accuracy", mesh_acc);

# 设置边界条件
set("x min bc", "Periodic");
set("x max bc", "Periodic");
set("y min bc", "Periodic");
set("y max bc", "Periodic");
set("z min bc", "PML");
set("z max bc", "PML");

# 设置波长和时间
set("wavelength start", lam_start);
set("wavelength stop", lam_stop);
set("simulation time", sim_time);

?"[INFO] FDTD 区域设置完成";

# ====================== 添加光源和监视器 ======================
?"[STEP] 添加光源和监视器...";

# 计算关键位置
z_sio2_top = z0 + air_bot + t_au_bot + t_sio2_bot + t_nbtiin + t_sio2_top;
src_z = z0 + lz*0.5 - 0.1*air_top;      # 光源位置
refl_z = z_sio2_top + 0.10*air_top;     # 反射监视器位置
trans_z = z0 + 0.10*air_bot;            # 透射监视器位置

# 添加平面波光源
addplane;
set("name", "src");
set("injection axis", "z");
set("direction", "Backward");
set("x span", px);
set("y span", py);
set("z", src_z);
set("wavelength start", lam_start);
set("wavelength stop", lam_stop);

# 添加反射功率监视器
addpower;
set("name", "R_monitor");
set("monitor type", "2D Z-normal");
set("x span", px);
set("y span", py);
set("z", refl_z);
set("override global monitor settings", 1);
set("use source limits", 1);

# 添加透射功率监视器
addpower;
set("name", "T_monitor");
set("monitor type", "2D Z-normal");
set("x span", px);
set("y span", py);
set("z", trans_z);
set("override global monitor settings", 1);
set("use source limits", 1);

?"[INFO] 光源和监视器添加完成";

# ====================== 定义结构构建函数 ======================
# 添加层结构的函数
function add_layer(layer_name, material_name, z_center, thickness) {
    addrect;
    set("name", layer_name);
    set("material", material_name);
    set("x span", px);
    set("y span", py);
    set("z", z_center);
    set("z span", thickness);
}

# 构建完整结构的函数
function build_structure() {
    ?"[INFO] 构建多层结构...";
    
    # 自下而上构建层结构
    current_z = z0;
    
    # 底部空气层
    z_center = current_z + air_bot * 0.5;
    add_layer("air_bottom", "Air", z_center, air_bot);
    current_z = current_z + air_bot;
    
    # 底部金层
    z_center = current_z + t_au_bot * 0.5;
    add_layer("Au_bottom", "Au (Gold) - Johnson and Christy", z_center, t_au_bot);
    current_z = current_z + t_au_bot;
    
    # 底部二氧化硅层
    z_center = current_z + t_sio2_bot * 0.5;
    add_layer("SiO2_bottom", "SiO2 (Glass) - Palik", z_center, t_sio2_bot);
    current_z = current_z + t_sio2_bot;
    
    # NbTiN 层
    z_center = current_z + t_nbtiin * 0.5;
    add_layer("NbTiN", "NbTiN_data", z_center, t_nbtiin);
    current_z = current_z + t_nbtiin;
    
    # 顶部二氧化硅层
    z_center = current_z + t_sio2_top * 0.5;
    add_layer("SiO2_top", "SiO2 (Glass) - Palik", z_center, t_sio2_top);
    z_sio2_top_surface = current_z + t_sio2_top;
    current_z = current_z + t_sio2_top;
    
    # 顶部空气层
    z_center = current_z + air_top * 0.5;
    add_layer("air_top", "Air", z_center, air_top);
    
    # 添加 Au 小方块
    patch_z = z_sio2_top_surface + patch_t*0.5 + patch_gap;
    
    # 随机位置（确保不超出边界）
    max_offset_x = 0.5*px - 0.5*patch_w;
    max_offset_y = 0.5*py - 0.5*patch_l;
    
    if (max_offset_x > 0) {
        patch_x = (rand(1) - 0.5) * 2 * max_offset_x;
    } else {
        patch_x = 0;
    }
    
    if (max_offset_y > 0) {
        patch_y = (rand(1) - 0.5) * 2 * max_offset_y;
    } else {
        patch_y = 0;
    }
    
    addrect;
    set("name", "Au_patch");
    set("material", "Au (Gold) - Johnson and Christy");
    set("x", patch_x);
    set("x span", patch_w);
    set("y", patch_y);
    set("y span", patch_l);
    set("z", patch_z);
    set("z span", patch_t);
    
    ?"[INFO] 结构构建完成";
}

# 清除结构的函数
function clear_structure() {
    structure_names = ["Au_patch", "SiO2_top", "NbTiN", "SiO2_bottom", "Au_bottom", "air_top", "air_bottom"];
    for (i = 1; i <= length(structure_names); i = i + 1) {
        if (find(structure_names(i))) {
            select(structure_names(i));
            delete;
        }
    }
    ?"[INFO] 结构清除完成";
}

# 获取监视器数据的函数
function get_monitor_data(monitor_name) {
    # 尝试获取归一化数据
    try {
        result = getresult(monitor_name, "T");
        wavelength = result.lambda;
        fraction = result.T;
        return {wavelength, fraction};
    } catch (error_msg) {
        # 如果失败，手动计算
        power_result = getresult(monitor_name, "power");
        wavelength = power_result.lambda;
        monitor_power = power_result.power;
        
        sp = sourcepower("src");
        fraction = monitor_power / sp;
        return {wavelength, fraction};
    }
}

# ====================== 执行仿真 ======================
?"[RUN] 开始基准仿真（仅光源）...";

# A) 仅光源仿真（归一化基准）
clear_structure();
run;

# 获取基准数据
ref_data_R = get_monitor_data("R_monitor");
ref_data_T = get_monitor_data("T_monitor");
lam_R_ref = ref_data_R{1};
R_ref = ref_data_R{2};
lam_T_ref = ref_data_T{1};
T_ref = ref_data_T{2};

?"[INFO] 基准仿真完成，数据点数: " + num2str(length(lam_R_ref));

# B) 带结构仿真
?"[RUN] 开始结构仿真...";
build_structure();
run;

# 获取结构数据
struct_data_R = get_monitor_data("R_monitor");
struct_data_T = get_monitor_data("T_monitor");
lam_R = struct_data_R{1};
R_data = struct_data_R{2};
lam_T = struct_data_T{1};
T_data = struct_data_T{2};

?"[INFO] 结构仿真完成，数据点数: " + num2str(length(lam_R));

# ====================== 保存结果 ======================
?"[STEP] 保存仿真结果...";

# 获取输出目录
output_dir = filebasename(NK_FILE);
if (output_dir == "") {
    output_dir = pwd;
}

# 保存基准数据
ref_file = output_dir + "/RT_reference.txt";
ref_data = [lam_R_ref*1e6, R_ref, T_ref];  # 转换为 nm
write(ref_file, "wavelength_nm,R_ref,T_ref");
write(ref_file, ref_data, "a");
?"[INFO] 基准数据已保存: " + ref_file;

# 保存结构数据
struct_file = output_dir + "/RT_structure.txt";
struct_data_matrix = [lam_R*1e6, R_data, T_data];  # 转换为 nm
write(struct_file, "wavelength_nm,R,T");
write(struct_file, struct_data_matrix, "a");
?"[INFO] 结构数据已保存: " + struct_file;

# ====================== 输出关键结果 ======================
?"";
?"========================================";
?"关键波长的仿真结果:";
?"========================================";

for (i = 1; i <= length(report_wavelengths); i = i + 1) {
    target_wl = report_wavelengths(i);
    
    # 插值获取目标波长的值
    R_val = interp(lam_R, R_data, target_wl);
    T_val = interp(lam_T, T_data, target_wl);
    
    # 确保非负
    R_val = max(0, R_val);
    T_val = max(0, T_val);
    
    result_str = "λ = " + num2str(target_wl*1000, "%4.0f") + " nm:  " +
                 "R = " + num2str(R_val, "%.4f") + ",  " +
                 "T = " + num2str(T_val, "%.4f") + ",  " +
                 "(R+T = " + num2str(R_val + T_val, "%.4f") + ")";
    ?result_str;
}

?"========================================";

# ====================== 完成 ======================
redrawon;
?"";
?"[完成] 归一化 + 结构化 两段仿真全部完成";
?"基准数据: " + ref_file;
?"结构数据: " + struct_file;
?"关键波长 R/T 已打印";
?"";
