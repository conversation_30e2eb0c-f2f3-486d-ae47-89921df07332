# 手动创建 NbTiN 材料指南

由于您的 Lumerical 版本不支持 `materialimport` 函数，需要手动创建 NbTiN 材料。

## 方法一：使用脚本创建简化材料（推荐）

我已经创建了 `compatible_lumerical_script.lsf`，它会自动创建一个简化的 NbTiN 材料模型。

**使用步骤：**
1. 在 Lumerical Script Editor 中打开 `compatible_lumerical_script.lsf`
2. 直接运行脚本
3. 脚本会自动创建 NbTiN 材料并运行仿真

## 方法二：手动在材料库中创建 NbTiN

### 步骤 1：打开材料数据库
1. 在 Lumerical 中，点击菜单 `Materials` → `Material Database`
2. 或者按快捷键 `Ctrl+M`

### 步骤 2：创建新材料
1. 点击 `Add Material` 按钮
2. 选择 `Dielectric` 类型
3. 设置材料名称为 `NbTiN_data`

### 步骤 3：设置光学常数
根据您的 NbTiN 数据，设置以下参数：

**选项 A：使用固定折射率（简化模型）**
- Refractive Index: 2.0
- Imaginary Refractive Index: 1.5

**选项 B：使用色散模型（更精确）**
如果您有详细的 n,k 数据，可以：
1. 选择 `Sampled Data` 模型
2. 手动输入波长和对应的 n,k 值
3. 或者导入数据文件

### 步骤 4：保存材料
1. 点击 `OK` 保存材料
2. 材料会出现在材料库中

### 步骤 5：在脚本中使用
创建材料后，可以在脚本中直接使用：
```lsf
set("material", "NbTiN_data");
```

## 方法三：修改脚本使用现有材料

如果不想创建新材料，可以用现有材料替代：

```lsf
# 将 NbTiN 层替换为其他材料
set("material", "Si (Silicon) - Palik");  # 使用硅
# 或者
set("material", "TiN");  # 如果有 TiN 材料
```

## 典型 NbTiN 光学常数参考

在不同波长下的典型值：

| 波长 (nm) | n    | k    |
|-----------|------|------|
| 800       | 2.4  | 2.0  |
| 1000      | 2.2  | 1.8  |
| 1300      | 2.0  | 1.6  |
| 1550      | 1.8  | 1.4  |
| 2000      | 1.6  | 1.2  |

## 推荐使用方案

**立即可用：** 使用 `compatible_lumerical_script.lsf`，它包含了简化的 NbTiN 模型

**精确仿真：** 手动创建材料，输入您的精确 n,k 数据

**快速测试：** 使用现有的类似材料（如 TiN 或 Si）进行概念验证

## 注意事项

1. **简化模型的局限性：** 固定的 n,k 值不能反映材料的色散特性
2. **精度影响：** 材料参数的准确性直接影响仿真结果
3. **版本兼容性：** 较新版本的 Lumerical 支持更多的材料导入功能

## 验证材料设置

创建材料后，可以通过以下方式验证：

```lsf
# 检查材料是否存在
if (materialexists("NbTiN_data")) {
    ?"NbTiN 材料创建成功";
} else {
    ?"NbTiN 材料不存在，请检查创建过程";
}
```
