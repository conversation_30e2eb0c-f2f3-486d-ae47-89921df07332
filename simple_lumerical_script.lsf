# 简化版 Lumerical FDTD 脚本
# 直接在 Lumerical Script Editor 中运行
# 
# 使用说明：
# 1. 在 Lumerical FDTD 中打开 Script Editor
# 2. 复制粘贴此脚本
# 3. 修改下面的 NK_FILE 路径为您的材料文件
# 4. 点击运行

# ====================== 配置 ======================
# 修改这里：您的 NbTiN 材料文件路径
NK_FILE = "C:/Users/<USER>/Desktop/NbTiN_nk.csv";

# 结构参数
px = 0.3e-6;      # 单胞 x 尺寸 (300 nm)
py = 0.3e-6;      # 单胞 y 尺寸 (300 nm)
air_bot = 0.3e-6;    # 底部空气 (300 nm)
au_bot = 0.08e-6;    # 底部金层 (80 nm)
sio2_bot = 0.15e-6;  # 底部 SiO2 (150 nm)
nbtiin = 0.019e-6;   # NbTiN 层 (19 nm)
sio2_top = 0.1e-6;   # 顶部 SiO2 (100 nm)
air_top = 0.3e-6;    # 顶部空气 (300 nm)

# Au 小方块
patch_size = 0.08e-6;  # 80 nm
patch_thick = 0.02e-6; # 20 nm

# 仿真参数
lambda_min = 800e-9;   # 800 nm
lambda_max = 2000e-9;  # 2000 nm

# ====================== 初始化 ======================
?"开始仿真...";
switchtolayout;
deleteall;
redrawoff;

# ====================== 导入材料 ======================
if (materialexists("NbTiN_data")) {
    deletematerial("NbTiN_data");
}

if (fileexists(NK_FILE)) {
    materialimport(NK_FILE, "NbTiN_data");
    ?"材料导入成功";
} else {
    ?"错误：找不到材料文件 " + NK_FILE;
    ?"请检查文件路径";
    break;
}

# ====================== 设置仿真区域 ======================
lz = air_bot + au_bot + sio2_bot + nbtiin + sio2_top + air_top;
z0 = -lz/2;

addfdtd;
set("x span", px);
set("y span", py);
set("z span", lz);
set("x min bc", "Periodic");
set("x max bc", "Periodic");
set("y min bc", "Periodic");
set("y max bc", "Periodic");
set("z min bc", "PML");
set("z max bc", "PML");
set("mesh accuracy", 3);

# ====================== 添加光源 ======================
addplane;
set("name", "source");
set("injection axis", "z");
set("direction", "Backward");
set("x span", px);
set("y span", py);
set("z", z0 + lz - 0.1*air_top);
set("wavelength start", lambda_min);
set("wavelength stop", lambda_max);

# ====================== 添加监视器 ======================
# 反射监视器
addpower;
set("name", "R");
set("monitor type", "2D Z-normal");
set("x span", px);
set("y span", py);
set("z", z0 + air_bot + au_bot + sio2_bot + nbtiin + sio2_top + 0.05*air_top);

# 透射监视器
addpower;
set("name", "T");
set("monitor type", "2D Z-normal");
set("x span", px);
set("y span", py);
set("z", z0 + 0.05*air_bot);

# ====================== 第一次仿真：仅光源 ======================
?"运行基准仿真（仅光源）...";
run;

# 获取基准数据
R_ref = getresult("R", "power");
T_ref = getresult("T", "power");
lambda_ref = R_ref.lambda;
P_ref_R = R_ref.power;
P_ref_T = T_ref.power;

?"基准仿真完成";

# ====================== 添加结构 ======================
?"添加多层结构...";

# 底部空气
addrect;
set("name", "air_bottom");
set("material", "Air");
set("x span", px);
set("y span", py);
set("z", z0 + air_bot/2);
set("z span", air_bot);

# 底部金层
addrect;
set("name", "Au_bottom");
set("material", "Au (Gold) - Johnson and Christy");
set("x span", px);
set("y span", py);
set("z", z0 + air_bot + au_bot/2);
set("z span", au_bot);

# 底部 SiO2
addrect;
set("name", "SiO2_bottom");
set("material", "SiO2 (Glass) - Palik");
set("x span", px);
set("y span", py);
set("z", z0 + air_bot + au_bot + sio2_bot/2);
set("z span", sio2_bot);

# NbTiN 层
addrect;
set("name", "NbTiN");
set("material", "NbTiN_data");
set("x span", px);
set("y span", py);
set("z", z0 + air_bot + au_bot + sio2_bot + nbtiin/2);
set("z span", nbtiin);

# 顶部 SiO2
addrect;
set("name", "SiO2_top");
set("material", "SiO2 (Glass) - Palik");
set("x span", px);
set("y span", py);
set("z", z0 + air_bot + au_bot + sio2_bot + nbtiin + sio2_top/2);
set("z span", sio2_top);

# 顶部空气
addrect;
set("name", "air_top");
set("material", "Air");
set("x span", px);
set("y span", py);
set("z", z0 + air_bot + au_bot + sio2_bot + nbtiin + sio2_top + air_top/2);
set("z span", air_top);

# Au 小方块
z_patch = z0 + air_bot + au_bot + sio2_bot + nbtiin + sio2_top + patch_thick/2 + 5e-9;
addrect;
set("name", "Au_patch");
set("material", "Au (Gold) - Johnson and Christy");
set("x span", patch_size);
set("y span", patch_size);
set("z", z_patch);
set("z span", patch_thick);

?"结构添加完成";

# ====================== 第二次仿真：带结构 ======================
?"运行结构仿真...";
run;

# 获取结构数据
R_struct = getresult("R", "power");
T_struct = getresult("T", "power");
lambda_struct = R_struct.lambda;
P_struct_R = R_struct.power;
P_struct_T = T_struct.power;

# 计算源功率
P_source = sourcepower("source");

# 计算反射率和透射率
Reflectance = P_struct_R / P_source;
Transmittance = P_struct_T / P_source;

?"结构仿真完成";

# ====================== 保存结果 ======================
# 保存到文件
output_file = "RT_results.txt";
data_matrix = [lambda_struct*1e9, Reflectance, Transmittance];
write(output_file, "wavelength_nm,R,T");
write(output_file, data_matrix, "a");

?"结果已保存到: " + output_file;

# ====================== 输出关键结果 ======================
?"";
?"========================================";
?"关键波长的仿真结果:";
?"========================================";

# 1000 nm 结果
lambda_1000 = 1000e-9;
R_1000 = interp(lambda_struct, Reflectance, lambda_1000);
T_1000 = interp(lambda_struct, Transmittance, lambda_1000);
?"λ = 1000 nm:  R = " + num2str(R_1000, "%.4f") + ",  T = " + num2str(T_1000, "%.4f") + ",  (R+T = " + num2str(R_1000+T_1000, "%.4f") + ")";

# 1500 nm 结果
lambda_1500 = 1500e-9;
R_1500 = interp(lambda_struct, Reflectance, lambda_1500);
T_1500 = interp(lambda_struct, Transmittance, lambda_1500);
?"λ = 1500 nm:  R = " + num2str(R_1500, "%.4f") + ",  T = " + num2str(T_1500, "%.4f") + ",  (R+T = " + num2str(R_1500+T_1500, "%.4f") + ")";

?"========================================";

# ====================== 完成 ======================
redrawon;
?"";
?"仿真完成！";
?"结果文件: " + output_file;
?"请查看上方的关键波长结果";

# 可选：绘制结果图
# plot(lambda_struct*1e9, Reflectance, "wavelength (nm)", "Reflectance", "Reflectance vs Wavelength");
# plot(lambda_struct*1e9, Transmittance, "wavelength (nm)", "Transmittance", "Transmittance vs Wavelength");
