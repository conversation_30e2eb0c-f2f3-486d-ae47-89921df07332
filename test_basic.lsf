# 基础测试脚本 - 验证 Lumerical 函数可用性

?"开始基础测试...";

# 测试基本函数
?"测试 switchtolayout...";
switchtolayout;

?"测试 deleteall...";
deleteall;

?"测试 redrawoff...";
redrawoff;

# 测试材料相关函数
?"测试材料函数...";
select("::model");

# 测试是否可以检查材料存在
?"测试 materialexists...";
test_exists = materialexists("Air");
?"Air 材料存在: " + num2str(test_exists);

# 测试添加简单结构
?"测试添加 FDTD 区域...";
addfdtd;
set("name", "test_FDTD");
set("dimension", "3D");
set("x span", 1e-6);
set("y span", 1e-6);
set("z span", 1e-6);

?"测试添加矩形...";
addrect;
set("name", "test_rect");
set("material", "Air");
set("x span", 500e-9);
set("y span", 500e-9);
set("z span", 100e-9);

?"测试添加光源...";
addplane;
set("name", "test_source");
set("injection axis", "z");
set("direction", "Backward");
set("x span", 500e-9);
set("y span", 500e-9);
set("z", 200e-9);

?"测试添加监视器...";
addpower;
set("name", "test_monitor");
set("monitor type", "2D Z-normal");
set("x span", 500e-9);
set("y span", 500e-9);
set("z", -200e-9);

?"测试 redrawon...";
redrawon;

?"基础测试完成！所有函数都可用。";
?"现在可以运行完整的仿真脚本了。";
