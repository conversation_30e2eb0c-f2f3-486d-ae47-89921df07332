# -*- coding: utf-8 -*-
"""
模拟 Lumerical API 模块
用于在没有 Lumerical 安装的环境中测试代码逻辑
"""

import numpy as np
import logging

logger = logging.getLogger(__name__)

class MockFDTD:
    """模拟 FDTD 对象"""
    
    def __init__(self, hide=False):
        self.commands = []
        self.variables = {}
        self.results = {}
        logger.info("Mock FDTD 对象已创建")
        
    def eval(self, command):
        """模拟执行 Lumerical 脚本命令"""
        self.commands.append(command)
        logger.debug(f"Mock eval: {command}")
        
        # 模拟一些特殊命令的响应
        if "sourcepower" in command:
            self.variables["sp"] = 1.0  # 模拟源功率为1
        
    def getresult(self, monitor_name, data_type):
        """模拟获取仿真结果"""
        logger.debug(f"Mock getresult: {monitor_name}, {data_type}")
        
        # 生成模拟的光谱数据
        wavelengths = np.linspace(0.8, 2.0, 50)  # 800-2000 nm
        
        if data_type == "power":
            # 模拟功率数据
            if "R_monitor" in monitor_name:
                # 模拟反射功率（随波长变化）
                power = 0.3 + 0.2 * np.sin(2 * np.pi * wavelengths) ** 2
            else:  # T_monitor
                # 模拟透射功率
                power = 0.5 + 0.1 * np.cos(2 * np.pi * wavelengths) ** 2
            
            return {
                "lambda": wavelengths,
                "power": power
            }
        
        elif data_type == "T":
            # 模拟归一化透射率/反射率
            if "R_monitor" in monitor_name:
                fraction = 0.3 + 0.2 * np.sin(2 * np.pi * wavelengths) ** 2
            else:
                fraction = 0.5 + 0.1 * np.cos(2 * np.pi * wavelengths) ** 2
            
            return {
                "lambda": wavelengths,
                "T": fraction
            }
        
        return {"lambda": wavelengths, "data": np.ones_like(wavelengths)}
    
    def getv(self, variable_name):
        """模拟获取变量值"""
        logger.debug(f"Mock getv: {variable_name}")
        return self.variables.get(variable_name, 1.0)

def FDTD(hide=False):
    """模拟 FDTD 构造函数"""
    return MockFDTD(hide=hide)

# 模拟其他可能需要的函数
def version():
    """返回模拟版本信息"""
    return "Mock Lumerical API v1.0"

# 添加一些常用的常量
class MockConstants:
    def __init__(self):
        self.c = 299792458  # 光速
        self.pi = np.pi

constants = MockConstants()

logger.info("Mock lumapi 模块已加载")
