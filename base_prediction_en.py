
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import meep as mp
from scipy import interpolate
from tqdm import tqdm
import networkx as nx
from collections import defaultdict
from sklearn.neural_network import MLPRegressor
import os
import time
import tmm

# ====================== Configuration Parameters ======================
class Config:
    MATERIAL_NK_FILES = {
        'TiN_4nm': 'data/TiN-4nm.csv',
        'TiO2': 'data/TiO2.csv',
        'TiN_30nm': 'data/TiN-30nm.csv',
        'Al2O3': 'data/Al2O3.txt'
    }

    EXPERIMENTAL_DATA = {
        'TiN_4nm': 'data/data.txt',
        'Al2O3': 'data/Al2O3.txt'
    }

    WAVELENGTHS = np.linspace(400, 800, 10)
    ANGLES = [0, 30, 45, 60]
    POLARIZATIONS = ['s', 'p']

    GRID_SIZE = (100, 50, 100)
    MATERIALS = ['TiN_4nm', 'TiO2', 'TiN_30nm', 'Al2O3']
    TARGET_THICKNESS = 45

    RESOLUTION = 30
    PML_THICKNESS = 0.3
    SIMULATION_TIME = 100

    VALIDATION_WAVELENGTHS = np.linspace(400, 800, 5)
    VALIDATION_ANGLE = 0
    VALIDATION_POLARIZATION = 's'

    OUTPUT_DIR = 'results/'
    SAVE_INTERVAL = 5

os.makedirs(Config.OUTPUT_DIR, exist_ok=True)

# ====================== Material Database ======================
class MaterialDatabase:
    def __init__(self, config):
        self.config = config
        self.materials_nk = {}
        self.materials_RT = {}
        self.wavelength_unit = 1e-3

        for material in config.MATERIALS:
            if material in config.MATERIAL_NK_FILES:
                self.load_material_data(material, config.MATERIAL_NK_FILES[material])

        for material, path in config.EXPERIMENTAL_DATA.items():
            self.load_experimental_RT(material, path)

    def load_material_data(self, name, file_path):
        if file_path.endswith('.csv'):
            data = pd.read_csv(file_path)
        else:
            data = pd.read_csv(file_path, sep='\t', header=None, names=['wavelength', 'n', 'k'])

        wavelengths = data['wavelength'].values * self.wavelength_unit
        n_values = data['n'].values
        k_values = data['k'].values

        self.materials_nk[name] = {
            'n': interpolate.interp1d(wavelengths, n_values, bounds_error=False, fill_value="extrapolate"),
            'k': interpolate.interp1d(wavelengths, k_values, bounds_error=False, fill_value="extrapolate")
        }

    def load_experimental_RT(self, name, file_path):
        if file_path.endswith('.txt'):
            data = pd.read_csv(file_path, sep='\t', header=None, names=['wavelength', 'R', 'T'])
        else:
            data = pd.read_csv(file_path)

        self.materials_RT[name] = data

    def get_nk(self, material_name, wavelength):
        if material_name not in self.materials_nk:
            raise ValueError(f"Material {material_name} nk data not found")
        n = self.materials_nk[material_name]['n'](wavelength)
        k = self.materials_nk[material_name]['k'](wavelength)
        return n, k

    def get_epsilon(self, material_name, wavelength):
        n, k = self.get_nk(material_name, wavelength)
        return (n + 1j * k) ** 2

    def get_medium(self, material_name, wavelength):
        return mp.Medium(epsilon=self.get_epsilon(material_name, wavelength))

    def get_interpolated_RT(self, material_name, wavelength):
        if material_name not in self.materials_RT:
            return None, None
        data = self.materials_RT[material_name]
        interpolator_R = interpolate.interp1d(data['wavelength'], data['R'], bounds_error=False, fill_value="extrapolate")
        interpolator_T = interpolate.interp1d(data['wavelength'], data['T'], bounds_error=False, fill_value="extrapolate")
        return interpolator_R(wavelength), interpolator_T(wavelength)

# ====================== Material Distribution ======================
class MaterialDistribution:
    def __init__(self, config):
        self.config = config
        self.grid = np.empty(config.GRID_SIZE, dtype=object)
        self.material_graph = nx.Graph()
        self.region_materials = {}
        self.material_regions = defaultdict(list)
        self.initialize_distribution()

    def initialize_distribution(self):
        for i in range(self.config.GRID_SIZE[0]):
            for j in range(self.config.GRID_SIZE[1]):
                for k in range(self.config.GRID_SIZE[2]):
                    mat_idx = np.random.randint(len(self.config.MATERIALS))
                    material = self.config.MATERIALS[mat_idx]
                    self.grid[i, j, k] = (material, None)
                    self.material_graph.add_node((i, j, k))

        self.get_connected_components()
        print(f"Initialization complete: Grid size {self.grid.shape}")

    def get_connected_components(self):
        for i in range(self.config.GRID_SIZE[0]):
            for j in range(self.config.GRID_SIZE[1]):
                for k in range(self.config.GRID_SIZE[2]):
                    self.grid[i, j, k] = (self.grid[i, j, k][0], None)

        self.material_graph = nx.Graph()
        for i in range(self.config.GRID_SIZE[0]):
            for j in range(self.config.GRID_SIZE[1]):
                for k in range(self.config.GRID_SIZE[2]):
                    self.material_graph.add_node((i, j, k))

# ====================== Meep Simulator ======================
class MeepSimulator:
    def __init__(self, material_db, config):
        self.material_db = material_db
        self.config = config

    def simulate(self, material_dist, wavelength, polarization, angle):
        return 0.5, 0.5

# ====================== Validation Module ======================
class ValidationModule:
    def __init__(self, material_db, config):
        self.material_db = material_db
        self.config = config
        self.simulator = MeepSimulator(material_db, config)

    def run_validation(self):
        print("Starting validation...")
        passed = True
        for wavelength in self.config.VALIDATION_WAVELENGTHS:
            R_sim, T_sim = self.simulator.simulate(None, wavelength, 's', self.config.VALIDATION_ANGLE)
            R_exp, T_exp = self.material_db.get_interpolated_RT('TiN_4nm', wavelength)
            if R_exp is None or T_exp is None:
                continue
            if abs(R_sim - R_exp) > 0.05 or abs(T_sim - T_exp) > 0.05:
                passed = False
                print(f"Validation failed at {wavelength}nm")
        return passed

# ====================== Material Optimizer ======================
class MaterialOptimizer:
    def __init__(self, config, material_db, simulator, material_dist):
        self.config = config
        self.material_db = material_db
        self.simulator = simulator
        self.material_dist = material_dist
        self.loss_history = []

    def optimize(self):
        print("Starting optimization...")
        for iteration in range(3):
            total_loss = 0
            for wavelength in self.config.WAVELENGTHS:
                for angle in self.config.ANGLES:
                    for pol in self.config.POLARIZATIONS:
                        R_sim, T_sim = self.simulator.simulate(self.material_dist, wavelength, pol, angle)
                        R_meas, T_meas = self.material_db.get_interpolated_RT('TiN_4nm', wavelength)
                        if R_meas is None or T_meas is None:
                            continue
                        loss = 0.7 * abs(R_sim - R_meas) + 0.3 * abs(T_sim - T_meas)
                        total_loss += loss
            avg_loss = total_loss / (len(self.config.WAVELENGTHS) * len(self.config.ANGLES) * len(self.config.POLARIZATIONS))
            self.loss_history.append(avg_loss)
            print(f"Iteration {iteration+1}: Avg loss = {avg_loss:.4f}")

# ====================== Main Function ======================
def main():
    config = Config()
    material_db = MaterialDatabase(config)
    validator = ValidationModule(material_db, config)
    if not validator.run_validation():
        print("Validation failed. Aborting.")
        return
    print("Validation passed. Proceeding to optimization.")
    material_dist = MaterialDistribution(config)
    simulator = MeepSimulator(material_db, config)
    optimizer = MaterialOptimizer(config, material_db, simulator, material_dist)
    optimizer.optimize()

if __name__ == "__main__":
    try:
        mp.check_meep()
        print("Meep installation verified.")
        main()
    except Exception as e:
        print(f"Meep check failed: {e}")
