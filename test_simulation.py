# -*- coding: utf-8 -*-
"""
Lumerical FDTD 仿真测试脚本
========================

这个脚本用于测试改进后的仿真代码的各个组件。
在运行完整仿真之前，可以使用这个脚本检查配置和依赖项。
"""

import sys
import logging
from pathlib import Path

# 设置简单的日志
logging.basicConfig(level=logging.INFO, format='%(levelname)s: %(message)s')
logger = logging.getLogger(__name__)

def test_imports():
    """测试必要的模块导入"""
    print("=" * 50)
    print("测试模块导入...")
    
    try:
        import pandas as pd
        import openpyxl
        print("✓ pandas 和 openpyxl 可用")
    except ImportError as e:
        print(f"✗ pandas/openpyxl 不可用: {e}")
        print("  建议: pip install pandas openpyxl")
    
    try:
        import numpy as np
        print("✓ numpy 可用")
    except ImportError as e:
        print(f"✗ numpy 不可用: {e}")
        print("  建议: pip install numpy")

def test_lumerical_api():
    """测试 Lumerical API"""
    print("=" * 50)
    print("测试 Lumerical API...")
    
    # 可能的 Lumerical 路径
    possible_paths = [
        r"C:\Program Files\Lumerical\FDTD\api\python",
        r"C:\Program Files\Lumerical\v232\api\python", 
        r"C:\Program Files\Lumerical\v231\api\python",
        r"C:\Program Files\Lumerical\v230\api\python",
        r"C:\Program Files\AnsysEM\Lumerical\api\python",
        r"C:\Program Files (x86)\Lumerical\FDTD\api\python",
    ]
    
    found_paths = []
    for path in possible_paths:
        if Path(path).exists():
            found_paths.append(path)
            print(f"✓ 找到 Lumerical 路径: {path}")
    
    if not found_paths:
        print("✗ 未找到 Lumerical 安装路径")
        print("  请确保 Lumerical FDTD 已正确安装")
        return False
    
    # 尝试导入 lumapi
    for path in found_paths:
        if path not in sys.path:
            sys.path.append(path)
    
    try:
        import lumapi
        print("✓ lumapi 导入成功")
        return True
    except ImportError as e:
        print(f"✗ lumapi 导入失败: {e}")
        return False

def test_config():
    """测试配置类"""
    print("=" * 50)
    print("测试配置类...")
    
    try:
        # 导入配置类
        sys.path.append('.')
        from config_example import CustomSimulationConfig
        
        config = CustomSimulationConfig()
        print("✓ 配置类创建成功")
        
        # 检查关键参数
        print(f"  材料文件: {config.NK_FILE}")
        print(f"  单胞尺寸: {config.UNIT_CELL_X} × {config.UNIT_CELL_Y} μm")
        print(f"  波长范围: {config.WAVELENGTH_START} - {config.WAVELENGTH_STOP} μm")
        print(f"  NbTiN 厚度: {config.NBTIN} μm")
        
        return True
    except Exception as e:
        print(f"✗ 配置类测试失败: {e}")
        return False

def test_material_data_format():
    """测试材料数据格式"""
    print("=" * 50)
    print("测试材料数据格式...")
    
    # 创建示例 CSV 数据
    sample_csv = """wavelength(nm),n,k
400,2.1,0.5
500,2.0,0.4
600,1.9,0.3
700,1.8,0.2
800,1.7,0.1"""
    
    try:
        # 写入临时文件
        test_file = Path("test_material.csv")
        with open(test_file, "w", encoding="utf-8") as f:
            f.write(sample_csv)
        
        print(f"✓ 创建测试材料文件: {test_file}")
        
        # 测试读取
        try:
            from pathlib import Path
            import csv
            
            with open(test_file, newline='', encoding="utf-8-sig") as f:
                reader = csv.DictReader(f)
                data = list(reader)
                print(f"✓ 成功读取 {len(data)} 行数据")
                print(f"  列名: {reader.fieldnames}")
                print(f"  第一行数据: {data[0]}")
        
        except Exception as e:
            print(f"✗ 读取测试文件失败: {e}")
        
        # 清理
        test_file.unlink()
        print("✓ 清理测试文件")
        
        return True
        
    except Exception as e:
        print(f"✗ 材料数据格式测试失败: {e}")
        return False

def test_simulation_class():
    """测试仿真类（不运行实际仿真）"""
    print("=" * 50)
    print("测试仿真类...")
    
    try:
        # 这里只测试类的创建，不测试 FDTD 初始化
        sys.path.append('.')
        from config_example import CustomSimulationConfig
        
        # 模拟导入主脚本的类（这里只是概念测试）
        config = CustomSimulationConfig()
        print("✓ 配置对象创建成功")
        
        # 测试一些计算
        total_thickness = (config.AIR_BOTTOM + config.AU_BOTTOM + 
                          config.SIO2_BOTTOM + config.NBTIN + 
                          config.SIO2_TOP + config.AIR_TOP)
        print(f"✓ 总结构厚度计算: {total_thickness} μm")
        
        # 测试路径处理
        nk_path = Path(config.NK_FILE)
        print(f"✓ 材料文件路径处理: {nk_path.name}")
        
        return True
        
    except Exception as e:
        print(f"✗ 仿真类测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("Lumerical FDTD 仿真环境测试")
    print("=" * 50)
    
    tests = [
        ("模块导入", test_imports),
        ("Lumerical API", test_lumerical_api),
        ("配置类", test_config),
        ("材料数据格式", test_material_data_format),
        ("仿真类", test_simulation_class),
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"✗ {test_name} 测试出现异常: {e}")
            results.append((test_name, False))
    
    # 总结
    print("=" * 50)
    print("测试结果总结:")
    print("=" * 50)
    
    passed = 0
    for test_name, result in results:
        status = "✓ 通过" if result else "✗ 失败"
        print(f"{test_name:20} {status}")
        if result:
            passed += 1
    
    print("=" * 50)
    print(f"总计: {passed}/{len(results)} 项测试通过")
    
    if passed == len(results):
        print("🎉 所有测试通过！可以运行完整仿真。")
    else:
        print("⚠️  部分测试失败，请检查环境配置。")
    
    return passed == len(results)

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
