RT_60degree_SP、RT_45degree_SP、RT_15degree_SP、RT_30degree_SP

用你的框架，完成这个推理，详细情况如下：

基本架构： air - TixNyOz (45nm) - Sapphire (430um,衬底）

上面的四个数据，分别是15度，S偏振RT和P偏振RT；然后30度、45度、60度，数据量很大，这些是实验数据，然后这个TixNyOz (45nm) ，具有特殊的光学特性，如果把这个45nm当做一层45nm layer，那么无论什么样的n和k，都无法满足实验测量的RT，因此，需要考虑，比如切片分层，比如，45nm分成三层，然后这三层的分别的n和k都需要提取，目前数据量有： 30度，4条，4个角度，供16条，每层需要n和k，因此最多可以分8层，这样数据自由度和求解自由度是匹配的，你看看，能否利用你当前研究的工具，把这个45nm具有局域效应的量子薄膜，通过分片提取的方式，来等效研究这个layer的光学特性。

45nm的那个量子薄膜，在Ai训练的过程中，如果说限制太多不容易收敛的话，其中的k-k（Kramers-Kronig）关系，可以不一定要符合的。目前是2D截面（假设是XZ截面）的TMM透反射，也可能需要介入XY面的分布。

这个代码需要实现对45纳米TiN的材料分布预测。具体数据是我刚刚给你的4个文件。因为这个材料它的分布是不均匀的，所以说我需要你把这个材料，分成一个长100，宽50，高100的，也就是说需要把45纳米TIN均分为100*50*高50的小模块。均分成功后，我们就拥有了一个structure，然后我们需要把这个三维Structure用另外一个material材料的矩阵，或者说三维矩阵来表示。然后我们先设定一个初始的随机的矩阵之后，把这个矩阵每一块每一个小的模块都变成材料一材料二材料三这样。然后我要求的，比如说是上下两个小模块都是材料1，那么此时他们两块被一起分成材料1。然后每一个大模块的材料都需要跟旁边的模块不同，我觉得这可以用并查集来解决？
4种填充材料分别是：①4nm的TiN②TiO2③30nm的TiN④Al2O3。现在，我把4nmTiN、Al2O3的测量R、T文件发给你，你提取出他们在不同波长下的性质，TiO2和30nmTiN的文件我随后再发给你，你可以先在代码中写上。然后，我们根据刚刚填充好的材料分布，使用FDTD来计算不同波长下、不同偏振、不同入射角度的R、T，之后根据真实的R、T（我发给你的4个文件），得到一个loss矩阵。之后再根据这个loss矩阵，反过来调整材料分布。你看看用什么神经网络。Al2O3的R、T是Al2O3.txt里，data.txt是4nmTiN的R、T，TiN-4nm是4nmTiN的n和k。你理解了吗？把你理解的逻辑写好发给我，包括用哪些文件做什么，然后再把代码写好发给我。需要使用linux的meep，然后使用pymeep来计算这个材料在不同波长下的R和T。