# 最简版本 - 使用现有材料替代 NbTiN

# ====================== 配置参数 ======================
px = 300e-9;         # 单胞 x 尺寸 (300 nm)
py = 300e-9;         # 单胞 y 尺寸 (300 nm)

# 层厚度（单位：米）
air_bot = 300e-9;    # 底部空气层
t_au_bot = 80e-9;    # 底部金层
t_sio2_bot = 150e-9; # 底部二氧化硅层
t_nbtiin = 19e-9;    # NbTiN 层
t_sio2_top = 100e-9; # 顶部二氧化硅层
air_top = 300e-9;    # 顶部空气层

# Au 小方块参数
patch_w = 80e-9;
patch_l = 80e-9;
patch_t = 20e-9;
patch_gap = 5e-9;

# 仿真参数
lam_start = 800e-9;
lam_stop = 2000e-9;
sim_time = 2000e-15;
mesh_acc = 3;

# ====================== 初始化 ======================
?"开始仿真...";
switchtolayout;
deleteall;
redrawoff;

# ====================== 设置 FDTD 区域 ======================
?"设置 FDTD 区域...";

lz = air_bot + t_au_bot + t_sio2_bot + t_nbtiin + t_sio2_top + air_top;
z0 = -lz/2;

addfdtd;
set("name", "FDTD");
set("dimension", "3D");
set("x span", px);
set("y span", py);
set("z span", lz);
set("mesh accuracy", mesh_acc);
set("x min bc", "Periodic");
set("x max bc", "Periodic");
set("y min bc", "Periodic");
set("y max bc", "Periodic");
set("z min bc", "PML");
set("z max bc", "PML");
set("wavelength start", lam_start);
set("wavelength stop", lam_stop);
set("simulation time", sim_time);

?"FDTD 区域设置完成";

# ====================== 添加光源和监视器 ======================
?"添加光源和监视器...";

z_sio2_top = z0 + air_bot + t_au_bot + t_sio2_bot + t_nbtiin + t_sio2_top;
src_z = z0 + lz*0.5 - 0.1*air_top;
refl_z = z_sio2_top + 0.10*air_top;
trans_z = z0 + 0.10*air_bot;

# 光源
addplane;
set("name", "src");
set("injection axis", "z");
set("direction", "Backward");
set("x span", px);
set("y span", py);
set("z", src_z);
set("wavelength start", lam_start);
set("wavelength stop", lam_stop);

# 反射监视器
addpower;
set("name", "R_monitor");
set("monitor type", "2D Z-normal");
set("x span", px);
set("y span", py);
set("z", refl_z);
set("override global monitor settings", 1);
set("use source limits", 1);

# 透射监视器
addpower;
set("name", "T_monitor");
set("monitor type", "2D Z-normal");
set("x span", px);
set("y span", py);
set("z", trans_z);
set("override global monitor settings", 1);
set("use source limits", 1);

?"光源和监视器完成";

# ====================== 第一次仿真：仅光源 ======================
?"运行基准仿真...";
run;

R_ref_result = getresult("R_monitor", "power");
T_ref_result = getresult("T_monitor", "power");
lam_R_ref = R_ref_result.lambda;
P_R_ref = R_ref_result.power;

?"基准仿真完成";

# ====================== 构建结构 ======================
?"构建结构...";

current_z = z0;

# 底部空气层
z_center = current_z + air_bot * 0.5;
addrect;
set("name", "air_bottom");
set("material", "Air");
set("x span", px);
set("y span", py);
set("z", z_center);
set("z span", air_bot);
current_z = current_z + air_bot;

# 底部金层
z_center = current_z + t_au_bot * 0.5;
addrect;
set("name", "Au_bottom");
set("material", "Au (Gold) - Johnson and Christy");
set("x span", px);
set("y span", py);
set("z", z_center);
set("z span", t_au_bot);
current_z = current_z + t_au_bot;

# 底部二氧化硅层
z_center = current_z + t_sio2_bot * 0.5;
addrect;
set("name", "SiO2_bottom");
set("material", "SiO2 (Glass) - Palik");
set("x span", px);
set("y span", py);
set("z", z_center);
set("z span", t_sio2_bot);
current_z = current_z + t_sio2_bot;

# NbTiN 层 - 使用硅材料替代
z_center = current_z + t_nbtiin * 0.5;
addrect;
set("name", "NbTiN_layer");
set("material", "Si (Silicon) - Palik");
set("x span", px);
set("y span", py);
set("z", z_center);
set("z span", t_nbtiin);
current_z = current_z + t_nbtiin;

# 顶部二氧化硅层
z_center = current_z + t_sio2_top * 0.5;
addrect;
set("name", "SiO2_top");
set("material", "SiO2 (Glass) - Palik");
set("x span", px);
set("y span", py);
set("z", z_center);
set("z span", t_sio2_top);
z_sio2_top_surface = current_z + t_sio2_top;
current_z = current_z + t_sio2_top;

# 顶部空气层
z_center = current_z + air_top * 0.5;
addrect;
set("name", "air_top");
set("material", "Air");
set("x span", px);
set("y span", py);
set("z", z_center);
set("z span", air_top);

# Au 小方块
patch_z = z_sio2_top_surface + patch_t*0.5 + patch_gap;
addrect;
set("name", "Au_patch");
set("material", "Au (Gold) - Johnson and Christy");
set("x", 0);
set("x span", patch_w);
set("y", 0);
set("y span", patch_l);
set("z", patch_z);
set("z span", patch_t);

?"结构构建完成";

# ====================== 第二次仿真：带结构 ======================
?"运行结构仿真...";
run;

R_struct_result = getresult("R_monitor", "power");
T_struct_result = getresult("T_monitor", "power");
lam_R = R_struct_result.lambda;
P_R_struct = R_struct_result.power;
lam_T = T_struct_result.lambda;
P_T_struct = T_struct_result.power;

P_source = sourcepower("src");
R_data = P_R_struct / P_source;
T_data = P_T_struct / P_source;

?"结构仿真完成";

# ====================== 输出结果 ======================
?"";
?"========================================";
?"仿真结果 (使用 Si 替代 NbTiN):";
?"========================================";

lambda_1000 = 1000e-9;
R_1000 = interp(lam_R, R_data, lambda_1000);
T_1000 = interp(lam_T, T_data, lambda_1000);
?"λ = 1000 nm:  R = " + num2str(R_1000, "%.4f") + ",  T = " + num2str(T_1000, "%.4f");

lambda_1500 = 1500e-9;
R_1500 = interp(lam_R, R_data, lambda_1500);
T_1500 = interp(lam_T, T_data, lambda_1500);
?"λ = 1500 nm:  R = " + num2str(R_1500, "%.4f") + ",  T = " + num2str(T_1500, "%.4f");

?"========================================";

# 保存结果
output_file = "RT_results_Si.txt";
data_matrix = [lam_R*1e9, R_data, T_data];
write(output_file, "wavelength_nm,R,T");
write(output_file, data_matrix, "a");

redrawon;
?"仿真完成！结果已保存到: " + output_file;
?"注意: 此版本使用 Si 材料替代 NbTiN 进行概念验证";
