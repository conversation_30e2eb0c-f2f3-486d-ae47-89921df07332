import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import meep as mp
from scipy import interpolate
from tqdm import tqdm
import networkx as nx
from collections import defaultdict
from sklearn.neural_network import MLPRegressor
import os
import time
import tmm  # TMM计算库

# ====================== 配置参数 ======================
class Config:
    # 文件路径配置
    MATERIAL_NK_FILES = {
        'TiN_4nm': 'data/TiN-4nm.csv',     # 4nm TiN的n,k数据
        'TiO2': 'data/TiO2.csv',           # TiO2的n,k数据
        'TiN_30nm': 'data/TiN-30nm.csv',   # 30nm TiN的n,k数据
        'Al2O3': 'data/Al2O3.txt'         # Al2O3的n,k数据
    }
    
    EXPERIMENTAL_DATA = {
        'TiN_4nm': 'data/data.txt',       # 4nm TiN的实验R,T数据
        'Al2O3': 'data/Al2O3.txt'        # Al2O3的实验R,T数据
    }
    
    # 模拟参数
    WAVELENGTHS = np.linspace(400, 800, 10)  # 波长范围(nm)
    ANGLES = [0, 30, 45, 60]                # 入射角度(度)
    POLARIZATIONS = ['s', 'p']               # 偏振方式
    
    # 材料分布参数
    GRID_SIZE = (100, 50, 100)  # (x, y, z) 网格大小
    MATERIALS = ['TiN_4nm', 'TiO2', 'TiN_30nm', 'Al2O3']  # 四种材料
    TARGET_THICKNESS = 45  # 目标薄膜厚度(nm)
    
    # FDTD参数
    RESOLUTION = 30  # FDTD网格分辨率(points/μm)
    PML_THICKNESS = 0.3  # PML厚度(μm) 300nm
    SIMULATION_TIME = 100  # 模拟时间(fs)
    
    # 验证参数
    VALIDATION_WAVELENGTHS = np.linspace(400, 800, 5)  # 验证用波长点
    VALIDATION_ANGLE = 0  # 验证用入射角度
    VALIDATION_POLARIZATION = 's'  # 验证用偏振
    
    # 输出设置
    OUTPUT_DIR = 'results/'
    SAVE_INTERVAL = 5  # 每多少次迭代保存一次结果

# 确保输出目录存在
os.makedirs(Config.OUTPUT_DIR, exist_ok=True)

# ====================== 材料数据库 ======================
class MaterialDatabase:
    """
    材料光学常数数据库
    功能：加载和插值获取材料的n,k值及实验测量的R,T数据
    
    属性:
        materials_nk: 存储每种材料的n,k插值函数
        materials_RT: 存储实验测量的R,T数据
        
    方法:
        load_material_data: 加载材料的光学常数数据
        load_experimental_RT: 加载实验测量的R,T数据
        get_nk: 获取指定波长下的n,k值
        get_epsilon: 获取复介电常数
        get_medium: 创建Meep介质对象
        get_experimental_RT: 获取实验R,T数据
    """
    
    def __init__(self, config):
        self.config = config
        self.materials_nk = {}
        self.materials_RT = {}
        self.wavelength_unit = 1e-3  # 单位为微米
        
        # 加载所有材料数据
        for material in config.MATERIALS:
            if material in config.MATERIAL_NK_FILES:
                self.load_material_data(material, config.MATERIAL_NK_FILES[material])
                
        # 加载实验数据
        for material, path in config.EXPERIMENTAL_DATA.items():
            self.load_experimental_RT(material, path)
    
    def load_material_data(self, name, file_path):
        """
        加载材料的光学常数数据(n,k)
        
        参数:
            name: 材料名称
            file_path: 数据文件路径
            
        处理流程:
            1. 根据文件扩展名确定格式(csv或txt)
            2. 读取波长和对应的n,k值
            3. 创建插值函数
        """
        if file_path.endswith('.csv'):
            data = pd.read_csv(file_path)
        else:  # txt格式
            data = pd.read_csv(file_path, sep='\t', header=None, 
                              names=['wavelength', 'n', 'k'])
        
        wavelengths = data['wavelength'].values * self.wavelength_unit
        n_values = data['n'].values
        k_values = data['k'].values
        
        # 创建插值函数
        self.materials_nk[name] = {
            'n': interpolate.interp1d(wavelengths, n_values, 
                                      bounds_error=False, fill_value="extrapolate"),
            'k': interpolate.interp1d(wavelengths, k_values, 
                                      bounds_error=False, fill_value="extrapolate")
        }
    
    def load_experimental_RT(self, name, file_path):
        """
        加载实验测量的R,T数据
        
        参数:
            name: 材料名称
            file_path: 数据文件路径
        """
        if file_path.endswith('.txt'):
            data = pd.read_csv(file_path, sep='\t', header=None, 
                              names=['wavelength', 'R', 'T'])
        else:
            data = pd.read_csv(file_path)
            
        self.materials_RT[name] = data
        
    def get_nk(self, material_name, wavelength):
        """
        获取指定波长下的n,k值
        
        参数:
            material_name: 材料名称
            wavelength: 波长(微米)
            
        返回:
            (n,极 k): 折射率和消光系数
            
        示例:
            material_db.get_nk('TiN_4nm', 0.5) -> (1.8, 0.2)
        """
        if material_name not in self.materials_nk:
            raise ValueError(f"Material {material_name} nk data not found")
        
        n = self.materials_nk[material_name]['n'](wavelength)
        k = self.materials_nk[material_name]['k'](wavelength)
        return n, k
    
    def get_epsilon(self, material_name, wavelength):
        """
        获取指定波长下的复介电常数
        
        参数:
            material_name: 材料名称
            wavelength: 波长(微米)
            
        返回:
            complex: 复介电常数
            
        示例:
            material_db.get_epsilon('TiN_4nm', 0.5) -> (1.8+0.2j)**2
        """
        n, k = self.get_nk(material_name, wavelength)
        epsilon = (n + 1j * k) ** 2
        return epsilon
    
    def get_medium(self, material_name, wavelength):
        """创建Meep介质对象"""
        return mp.Medium(epsilon=self.get_epsilon(material_name, wavelength))
    
    def get_experimental_RT(self, material_name, wavelength):
        """
        获取实验R,T数据
        
        参数:
            material_name: 材料名称
            wavelength: 波长(nm)
            
        返回:
            (R, T): 反射率和透射率
        """
        if material_name not in self.materials_RT:
            return None, None
            
        data = self.materials极.RT[material_name]
        # 找到最接近的波长
        idx = np.abs(data['wavelength'] - wavelength).argmin()
        return data.iloc[idx]['R'], data.iloc[idx]['T']
    
    def get_interpolated_RT(self, material_name, wavelength):
        """
        获取插值后的实验R,T数据
        
        参数:
            material_name: 材料名称
            wavelength: 波长(nm)
            
        返回:
            (R, T): 插值后的反射率和透射率
        """
        if material_name not in self.materials_RT:
            return None, None
            
        data = self.materials_RT[material_name]
        interpolator_R = interpolate.interp1d(data['wavelength'], data['R'], 
                                              bounds_error=False, fill_value="extrapolate")
        interpolator_T = interpolate.interp1d(data['wavelength'], data['T'], 
                                              bounds_error=False, fill_value="extrapolate")
        return interpolator_R(wavelength), interpolator_T(wavelength)

# ====================== 材料分布 ======================
class MaterialDistribution:
    """
    材料分布管理
    功能：创建和管理三维材料分布结构
    
    属性:
        grid: 三维网格，存储每个单元的(材料类型, 所属区域)
        material_graph: 并查集数据结构
        region_materials: 区域到材料的映射
        material_regions: 材料到区域的映射
        
    方法:
        initialize_distribution: 初始化随机材料分布
        assign_material: 分配材料到指定位置
        get_connected_components: 使用并查集连接相邻相同材料
        optimize_distribution: 优化材料分布
        visualize_slice: 可视化一个切片
    """
    
    def __init__(self, config):
        self.config = config
        self.grid = np.empty(config.GRID_SIZE, dtype=object)
        self.material_graph = nx.Graph()
        self.region_materials = {}  # 区域ID -> 材料类型
        self.material_regions = defaultdict(list)  # 材料类型 -> 区域ID列表
        self.initialize_distribution()
    
    def initialize_distribution(self):
        """
        初始化随机材料分布
        
        流程:
            1. 为每个网格单元随机分配材料
            2. 初始化并查集结构
            3. 连接相邻的相同材料单元
        """
        # 随机初始化材料分布
        for i in range(self.config.GRID_SIZE[0]):
            for j in range(self.config.GRID_SIZE[1]):
                for k in range(self.config.GRID_SIZE[2]):
                    mat_idx = np.random.randint(len(self.config.MATERIALS))
                    material = self.config.MATERIALS[mat_idx]
                    self.grid[i, j, k] = (material, None)
                    self.material_graph.add_node((i, j, k))
        
        # 连接相邻的相同材料单元
        self.get_connected_components()
        
        print(f"初始化完成: {self.grid.shape} 网格大小")
    
    def get_connected_components(self):
        """
        使用并查集连接相邻相同材料单元
        
        流程:
            1. 遍历所有网格单元
            2. 检查6个方向的邻居单元
            3. 如果材料相同，则连接它们
        """
        # 首先重置所有区域
        for i in range(self.config.GRID_SIZE[0]):
            for j in range(self.config.GRID_SIZE[1]):
                for k in range(self.config.GRID_SIZE[2]):
                    self.grid[i, j, k] = (self.grid[i, j, k][0], None)
        
        # 重置图结构
        self.material_graph = nx.Graph()
        for i in range(self.config.GRID_SIZE[0]):
            for j in range(self.config.GRID_SIZE[1]):
                for k in range(self.config.GRID_SIZE[2]):
                    self.material_graph.add_node((i, j, k))
        
        # 添加相同材料相邻单元的边
        directions = [
            (1, 0, 0), (-1, 0, 0),
            (0, 1, 0), (0, -1, 0),
            (0, 0, 1), (0, 0, -1)
        ]
        
        for i in range(self.config.GRID_SIZE[0]):
            for j in range(self.config.GRID_SIZE[1]):
                for k in range(self.config.GRID_SIZE[2]):
                    current_mat = self.grid[i, j, k][0]
                    
                    for dx, dy, dz in directions:
                        ni, nj, nk = i+dx, j+dy, k+dz
                        if (0 <= ni < self.config.G极ID_SIZE[0] and 
                            0 <= nj < self.config.GRID_SIZE[1] and 
                            0 <= nk < self.config.GRID_SIZE[2]):
                            
                            neighbor_mat = self.grid[ni, nj, nk][0]
                            if current_mat == neighbor_mat:
                                self.material_graph.add_edge((i, j, k), (ni, nj, nk))
        
        # 识别连通组件（区域）
        regions = list(nx.connected_components(self.material_graph))
        
        # 更新网格和区域映射
        for region_id, component in enumerate(regions):
            material = self.grid[next(iter(component))][0]  # 获取区域的材料类型
            
            # 更新区域映射
            self.region_materials[region_id] = material
            if material not in self.material_regions:
                self.material_regions[material] = []
            self.material_regions[material].append(region_id)
            
            # 更新网格单元
            for pos in component:
                i, j, k = pos
                self.grid[i, j, k] = (material, region_id)
    
    def assign_material(self, position, material):
        """
        分配材料到指定位置
        
        参数:
            position: (i, j, k) 三维坐标
            material: 材料名称
        """
        i, j, k = position
        self.grid[i, j, k] = (material, None)
        self.get_connected_components()  # 重新计算区域
    
    def optimize_distribution(self, loss_matrix, learning_rate=0.1, mutation_rate=0.1):
        """
        优化材料分布
        
        参数:
            loss_matrix: 每个区域的损失值矩阵
            learning_rate: 学习率
            mutation_rate: 变异率
            
        流程:
            1. 基于损失矩阵计算每个材料的适应度
            2. 交叉操作：随机选择区域进行材料交换
            3. 变异操作：以一定概率随机改变区域材料
        """
        # 计算每种材料的平均损失
        material_loss = {}
        for material in self.config.MATERIALS:
            if material in self.material_regions:
                regions = self.material_regions[material]
                total_loss = sum(loss_matrix[region] for region in regions)
                material_loss[material] = total_loss / len(regions)
            else:
                material_loss[material] = float('inf')  # 未出现的材料损失设为无穷大
        
        # 按损失排序材料
        sorted_materials = sorted(self.config.MATERIALS, key=lambda m: material_loss[m])
        
        # 交叉操作：将高损失区域替换为低损失材料
        for material, regions in list(self.material_regions.items()):
            if material == sorted_materials[-1]:  # 最高损失材料
                new_material = sorted_materials[0]  # 最低损失材料
                for region in regions:
                    # 找到该区域的所有单元并更新材料
                    for i in range(self.config.GRID_SIZE[0]):
                        for j in range(self.config.GRID_SIZE[1]):
                            for k in range(self.config.GRID_SIZE[2]):
                                if self.grid[i, j, k][1] == region:
                                    self.grid[i, j, k] = (new_material, region)
                self.region_materials[region] = new_material
                
        # 变异操作：随机改变一些区域
        for region in list(self.region_materials.keys()):
            if np.random.rand() < mutation_rate:
                current_material = self.region_materials[region]
                # 随机选择新材料（不同于当前）
                new_material = np.random.choice([m for m in self.config.MATERIALS if m != current_material])
                
                # 更新该区域的所有单元
                for i in range(self.config.GRID_SIZE[0]):
                    for j in range(self.config.GRID_SIZE[1]):
                        for k in range(self.config.GRID_SIZE[2]):
                            if self.grid[i, j, k][1] == region:
                                self.grid[i, j, k] = (new_material, region)
                self.region_materials[region] = new_material
        
        # 更新区域映射
        self.get_connected_components()
    
    def visualize_slice(self, slice_idx, axis='z', save_path=None):
        """
        可视化一个切片
        
        参数:
            slice_idx: 切片索引
            axis: 切片轴 ('x', 'y', 'z')
            save_path: 图像保存路径
        """
        material_to_idx = {mat: idx for idx, mat in enumerate(self.config.MATERIALS)}
        
        if axis == 'x':
            slice_data = np.zeros((self.config.GRID_SIZE[1], self.config.GRID_SIZE[2]))
            for j in range(self.config.GRID_SIZE[1]):
                for k in range(self.config.GRID_SIZE[2]):
                    slice_data[j, k] = material_to_idx[self.grid[slice_idx, j, k][0]]
        elif axis == 'y':
            slice_data = np.zeros((self.config.GRID_SIZE[0], self.config.GRID_SIZE[2]))
            for i in range(self.config.GRID_SIZE[0]):
                for k in range(self.config.GRID_SIZE[2]):
                    slice_data[i, k] = material_to_idx[self.grid[i, slice_idx, k][0]]
        else:  # z轴
            slice_data = np.zeros((self.config.GRID_SIZE[0], self.config.GRID_SIZE[1]))
            for i in range(self.config.GRID_SIZE[0]):
                for j in range(self.config.GRID_SIZE[1]):
                    slice_data[i, j] = material_to_idx[self.grid[i, j, slice_idx][0]]
        
        plt.figure(figsize=(10, 8))
        plt.imshow(slice_data, cmap='viridis')
        plt.colorbar(ticks=range(len(self.config.MATERIALS)), 
                    label='Materials')
        plt.clim(0, len(self.config.MATERIALS) - 1)
        plt.title(f'Material Distribution along {axis}-axis at index {slice_idx}')
        plt.xlabel('Y' if axis == 'x' else 'X')
        plt.ylabel('X' if axis == 'y' else 'Y')
        
        if save_path:
            plt.savefig(save_path, dpi=150)
            plt.close()
        else:
            plt.show()

# ====================== Meep FDTD 模拟器 ======================
class MeepSimulator:
    """
    基于Meep的FDTD模拟器
    功能：创建分层结构并计算R,T
    
    属性:
        material_db: 材料数据库实例
        config: 配置对象
        
    方法:
        create_layered_structure: 创建分层介质结构
        simulate: 执行FDTD模拟
        simulate_validation_structure: 执行验证结构模拟
    """
    
    def __init__(self, material_db, config):
        self.material_db = material_db
        self.config = config
    
    def create_layered_structure(self, material_dist, wavelength):
        """
        从材料分布创建分层结构
        
        参数:
            material_dist: MaterialDistribution实例
            wavelength: 当前模拟波长(微米)
            
        返回:
            geometry: Meep几何结构
            layer_thickness: 各层厚度列表
            materials: 各层材料列表
        """
        # 计算层厚度
        unit_thickness = self.config.TARGET_THICKNESS / self.config.GRID_SIZE[2] * 1e-3  # 转换为μm
        layer_thickness = [unit_thickness] * self.config.GRID_SIZE[2]
        
        # 空气层
        air = mp.Medium(epsilon=1.0)
        
        # 蓝宝石衬底 (Al2O3)
        substrate_thickness = 0.430  # 蓝宝石衬底厚度(μm)
        substrate = self.material_db.get_medium('Al2O3', wavelength)
        
        # 创建几何结构列表
        geometry = []
        current_height = 0
        z_positions = []
        
        # 添加材料层
        for k in range(self.config.GRID_SIZE[2]):
            # 计算当前z位置
            z_center = current_height + layer_thickness[k] / 2
            z_positions.append(z_center)
            current_height += layer_thickness[k]
            
            # 获取材料(简化：取z=k平面上的主要材料)
            mat_counts = defaultdict(int)
            for i in range(self.config.GRID_SIZE[0]):
                for j in range(self.config.GRID_SIZE[1]):
                    material = material_dist.grid[i, j, k][极]
                    mat_counts[material] += 1
                    
            # 取出现最频繁的材料
            layer_material = max(mat_counts, key=mat_counts.get)
            medium = self.material_db.get_medium(layer_material, wavelength)
            
            # 添加材料层
            geometry.append(mp.Block(
                size=mp.Vector3(mp.inf, mp.inf, layer_thickness[k]),
                center=mp.Vector3(0, 0, z_center),
                material=medium
            ))
        
        # 添加衬底层
        current_height += layer_thickness[-1] / 2
        z_center_sub = current_height + substrate_thickness / 2
        geometry.append(mp.Block(
            size=mp.Vector3(mp.inf, mp.inf, substrate_thickness),
            center=mp.Vector3(0, 0, z_center_sub),
            material=substrate
        ))
        
        # 返回几何结构、层厚度和材料类型
        return geometry, layer_thickness, [mat for mat in mat_counts.keys()]
    
    def simulate(self, material_dist, wavelength, polarization, angle):
        """
        执行FDTD模拟计算
        
        参数:
            material_dist: MaterialDistribution实例
            wavelength: 波长(nm)
            polarization: 偏振 ('s'或'p')
            angle: 入射角度(度)
            
        返回:
            R, T: 反射率和透射率
        """
        wavelength_um = wavelength * 1e-3  # 转换为微米
        try:
            # 1. 创建几何结构
            geometry, layer_thickness, _ = self.create_layered_structure(material_dist, wavelength_um)
            total_height = sum(layer_thickness) + 0.430  # 薄膜+衬底高度(μm)
            
            # 2. 设置计算区域
            cell_size_z = total_height + 1.0  # 额外空间
            cell_size = mp.Vector3(0, 0, cell_size_z)  # 1D模拟
            
            # 3. 设置光源
            fcen = 1 / wavelength_um  # 中心频率
            src_pt = mp.Vector3(0, 0, -0.45 * cell_size_z)  # 源位置
            
            if polarization == 's':
                src_cmpt = mp.Ex
            else:  # p偏振
                src_cmpt = mp.Ez
            
            sources = [mp.Source(
                mp.GaussianSource(fcen, fwidth=0.1*fcen),
                component=src_cmpt,
                center=src_pt,
                size=mp.Vector3(0, 0, 0)
            )]
            
            # 4. 设置PML边界
            pml_layers = [mp.PML(self.config.PML_THICKNESS)]
            
            # 5. 设置模拟器
            sim = mp.Simulation(
                cell_size=cell_size,
                geometry=geometry,
                sources=sources,
                boundary_layers=pml_layers,
                dimensions=1,  # 1D模拟
                resolution=self.config.RESOLUTION
            )
            
            # 6. 添加通量监视器
            refl_pt = mp.Vector3(0, 0, -0.25 * cell_size_z)  # 反射通量位置
            tran_pt = mp.Vector3(0, 0, 0.25 * cell_size_z)   # 透射通量位置
            
            refl_flux = sim.add_flux(fcen, 0, 1, mp.FluxRegion(center=refl_pt))
            tran_flux = sim.add_flux(fcen, 0, 1, mp.FluxRegion(center=tran_pt))
            
            # 7. 运行模拟
            sim.run(until_after_sources=mp.stop_when_fields_decayed(20, mp.Ez, refl_pt, 1e-3))
            
            # 8. 计算反射率和透射率
            incident_flux = mp.get_fluxes(refl_flux)[0]
            reflected_flux = mp.get_fluxes(refl_flux)[0]
            transmitted_flux = mp.get_fluxes(tran_flux)[0]
            
            # 对于反射率，需要参考入射通量
            R = -reflected_flux / incident_flux
            T = transmitted_flux / incident_flux
            
            return max(0, min(1, R)), max(0, min(1, T))
        
        except Exception as e:
            print(f"Meep simulation failed: {str(e)}")
            return 0.0, 0.0  # 返回默认值
    
       def simulate_validation_structure(self, structure_type, wavelength):
        """
        执行验证结构模拟
        
        参数:
            structure_type: 结构类型 ('Al2O3', 'TiN', 'Hybrid')
            wavelength: 波长(nm)
            
        返回:
            R, T: 反射率和透射率
        """
        wavelength_um = wavelength * 1e-3  # 转换为微米
        
        try:
            # 1. 创建几何结构
            # 总高度计算: PML(0.3μm) + 光源层(0.5μm) + 结构层(0.045μm) + 探测器层(0.5μm) + PML(0.3μm)
            total_height = 0.3 + 0.5 + 0.045 + 0.5 + 0.3  # μm
            
            # 2. 设置计算区域
            cell_size = mp.Vector3(1.0, 0, total_height)  # 1D模拟，x方向1μm
            
            # 3. 材料定义
            air = mp.Medium(epsilon=1.0)
            if structure_type == 'Al2O3':
                structure_medium = self.material_db.get_medium('Al2O3', wavelength_um)
            elif structure_type == 'TiN':
                structure_medium = self.material_db.get_medium('TiN_4nm', wavelength_um)
            else:  # Hybrid
                # 混合结构: 上半部分Al2O3, 下半部分TiN
                structure_medium1 = self.material_db.get_medium('Al2O3', wavelength_um)
                structure_medium2 = self.material_db.get_medium('TiN_4nm', wavelength_um)
            
            # 4. 创建几何结构列表
            geometry = []
            current_z = -total_height/2  # 从底部开始
            
            # 底部PML层 (0.3μm)
            pml_bottom = mp.Block(
                size=mp.Vector3(1.0, 0, 0.3),
                center=mp.Vector3(0, 0, current_z + 0.3/2),
                material=air
            )
            geometry.append(pml_bottom)
            current_z += 0.3
            
            # 透射探测器层 (0.5μm)
            tran_detector = mp.Block(
                size=mp.Vector3(1.0, 0, 0.5),
                center=mp.Vector3(0, 0, current_z + 0.5/2),
                material=air
            )
            geometry.append(tran_detector)
            current_z += 0.5
            
            # 结构层 (0.045μm)
            if structure_type == 'Hybrid':
                # 上半部分Al2O3 (0.0225μm)
                geometry.append(mp.Block(
                    size=mp.Vector3(1.0, 0, 0.0225),
                    center=mp.Vector3(0, 0, current_z + 0.0225/2),
                    material=structure_medium1
                ))
                # 下半部分TiN (0.0225μm)
                geometry.append(mp.Block(
                    size=mp.Vector3(1.0, 0, 0.0225),
                    center=mp.Vector3(0, 0, current_z + 0.0225 + 0.0225/2),
                    material=structure_medium2
                ))
                current_z += 0.045
            else:
                structure_layer = mp.Block(
                    size=mp.Vector3(1.0, 0, 0.045),
                    center=mp.Vector3(0, 0, current_z + 0.045/2),
                    material=structure_medium
                )
                geometry.append(structure_layer)
                current_z += 0.045
            
            # 光源层 (0.5μm)
            source_layer = mp.Block(
                size=mp.Vector3(1.0, 0, 0.5),
                center=mp.Vector3(0, 0, current_z + 0.5/2),
                material=air
            )
            geometry.append(source_layer)
            current_z += 0.5
            
            # 顶部PML层 (0.3μm)
            pml_top = mp.Block(
                size=mp.Vector3(1.0, 0, 0.3),
                center=mp.Vector3(0, 0, current_z + 0.3/2),
                material=air
            )
            geometry.append(pml_top)
            
            # 5. 设置光源
            fcen = 1 / wavelength_um  # 中心频率
            src_pt = mp.Vector3(0, 0, -total_height/2 + 0.3 + 0.5/2)  # 源位置在光源层中心
            
            # 使用s偏振
            src_cmpt = mp.Ex
            
            sources = [mp.Source(
                mp.GaussianSource(fcen, fwidth=0.1*fcen),
                component=src_cmpt,
                center=src_pt,
                size=mp.Vector3(0, 0, 0)
            )]
            
            # 6. 设置PML边界
            pml_layers = [mp.PML(self.config.PML_THICKNESS)]
            
            # 7. 设置模拟器
            sim = mp.Simulation(
                cell_size=cell_size,
                geometry=geometry,
                sources=sources,
                boundary_layers=pml_layers,
                dimensions=1,  # 1D模拟
                resolution=self.config.RESOLUTION
            )
            
            # 8. 添加通量监视器
            # 反射通量位置: 在光源层下方
            refl_pt = mp.Vector3(0, 0, -total_height/2 + 0.3 + 0.5/2 - 0.1)
            # 透射通量位置: 在透射探测器层上方
            tran_pt = mp.Vector3(0, 0, -total_height/2 + 0.3 + 0.5/2 + 0.1)
            
            refl_flux = sim.add_flux(fcen, 0, 1, mp.FluxRegion(center=refl_pt))
            tran_flux = sim.add_flux(fcen, 0, 1, mp.FluxRegion(center=tran_pt))
            
            # 9. 运行模拟
            sim.run(until_after_sources=mp.stop_when_fields_decayed(20, mp.Ez, refl_pt, 1e-3))
            
            # 10. 计算反射率和透射率
            incident_flux = mp.get_fluxes(refl_flux)[0]
            reflected_flux = np.abs(refl_flux.total_flux())[0]
            transmitted_flux = np.abs(tran_flux.total_flux())[0]
            
            # 对于反射率，需要参考入射通量
            R = reflected_flux / incident_flux
            T = transmitted_flux / incident_flux
            
            return max(0, min(1, R)), max(0, min(1, T))
        
        except Exception as e:
            print(f"Meep validation simulation failed: {str(e)}")
            return 0.0, 0.0  # 返回默认值
# ====================== 验证模块 ======================
class ValidationModule:
    """
    验证模块
    功能：验证Meep FDTD模拟结果与TMM计算结果是否一致
    
    属性:
        material_db: 材料数据库实例
        config: 配置对象
        simulator: Meep模拟器
        
    方法:
        calculate_tmm: 使用TMM计算反射率和透射率
        run_validation: 执行验证流程
    """
    
    def __init__(self, material_db, config):
        self.material_db = material_db
        self.config = config
        self.simulator = MeepSimulator(material_db, config)
    
    def calculate_tmm(self, structure_type, wavelength):
        """
        使用TMM计算反射率和透射率
        
        参数:
            structure_type: 结构类型 ('Al2O3', 'TiN', 'Hybrid')
            wavelength: 波长(nm)
            
        返回:
            R_tmm, T_tmm: TMM计算的反射率和透射率
        """
        wavelength_um = wavelength * 1e-3  # 转换为微米
        
        # 获取材料光学常数
        n_air = 1.0
        k_air = 0.0
        
        if structure_type == 'Al2O3':
            n1, k1 = self.material_db.get_nk('Al2O3', wavelength_um)
            layers = [
                {'d': 0.045, 'n': n1, 'k': k1}  # 45nm Al2O3
            ]
        elif structure_type == 'TiN':
            n1, k1 = self.material_db.get_nk('TiN_4nm', wavelength极_um)
            layers = [
                {'d': 0.045, 'n': n1, 'k': k1}  # 45nm TiN
            ]
        else:  # Hybrid
            n1, k1 = self.material_db.get_nk('Al2O3', wavelength_um)
            n2, k2 = self.material_db.get_nk('TiN_4nm', wavelength_um)
            layers = [
                {'d': 0.0225, 'n': n1, 'k': k1},  # 22.5nm Al2O3
                {'d': 0.0225, 'n': n2, 'k': k2}   # 22.5nm TiN
            ]
        
        # 创建TMM结构
        n_list = [n_air] + [layer['n'] for layer in layers] + [n_air]
        k_list = [k_air] + [layer['k'] for layer in layers] + [k_air]
        d_list = [0] + [layer['d'] for layer in layers] + [0]  # 厚度(μm)
        
        # 计算入射角(弧度)
        angle_rad = np.radians(self.config.VALIDATION_ANGLE)
        
        # 计算偏振分量
        if self.config.VALIDATION_POLARIZATION == 's':
            pol = 's'
        else:
            pol = 'p'
        
        # 使用TMM计算
        result = tmm.coh_tmm(
            pol, n_list, d_list, k_list, angle_rad, wavelength_um
        )
        
        R_tmm = result['R']
        T_tmm = result['T']
        
        return R_tmm, T_tmm
    
    def run_validation(self):
        """
        执行验证流程
        
        返回:
            bool: 验证是否通过
        """
        print("="*50)
        print("开始验证模块: 比较Meep FDTD与TMM计算结果")
        print("="*50)
        
        structures = ['Al2O3', 'TiN', 'Hybrid']
        results = []
        validation_passed = True
        
        for struct in structures:
            print(f"\n验证结构: {struct}")
            struct_results = []
            
            for wavelength in self.config.VALIDATION_WAVELENGTHS:
                # Meep模拟
                R_meep, T_meep = self.simulator.simulate_validation_structure(struct, wavelength)
                
                # TMM计算
                R_tmm, T_tmm = self.calculate_tmm(struct, wavelength)
                
                # 计算差异
                diff_R = abs(R_meep - R_tmm)
                diff_T = abs(T_meep - T_tmm)
                
                # 检查是否通过阈值
                threshold = 0.05  # 5%差异阈值
                passed = (diff_R < threshold) and (diff_T < threshold)
                
                if not passed:
                    validation_passed = False
                
                # 保存结果
                struct_results.append({
                    'wavelength': wavelength,
                    'R_meep': R_meep,
                    'T_meep': T_meep,
                    'R_tmm': R_tmm,
                    'T_tmm': T_tmm,
                    'diff_R': diff_R,
                    'diff_T': diff_T,
                    'passed': passed
                })
                
                print(f"波长 {wavelength}nm: "
                      f"Meep R={R_meep:.4f}, T={T_meep:.4f} | "
                      f"TMM R={R_tmm:.4f}, T={T_tmm:.4f} | "
                      f"差异 R={diff_R:.4f}, T={diff_T:.4f} | "
                      f"{'通过' if passed else '失败'}")
            
            results.append({
                'structure': struct,
                'results': struct_results
            })
        
        # 保存验证结果
        self.save_validation_results(results)
        
        # 可视化验证结果
        self.visualize_validation_results(results)
        
        print("\n验证结果:", "通过" if validation_passed else "失败")
        print("="*50)
        
        return validation_passed
    
    def save_validation_results(self, results):
        """保存验证结果到文件"""
        all_data = []
        for struct_data in results:
            struct = struct_data['structure']
            for res in struct_data['results']:
                all_data.append({
                    'structure': struct,
                    'wavelength': res['wavelength'],
                    'R_meep': res['R_meep'],
                    'T_meep': res['T_meep'],
                    'R_tmm': res['R_tmm'],
                    'T_tmm': res['T_tmm'],
                    'diff_R': res['diff_R'],
                    'diff_T': res['diff_T'],
                    'passed': res['passed']
                })
        
        df = pd.DataFrame(all_data)
        df.to_csv(f"{self.config.OUTPUT_DIR}/validation_results.csv", index=False)
    
    def visualize_validation_results(self, results):
        """可视化验证结果"""
        plt.figure(figsize=(15, 10))
        
        for i, struct_data in enumerate(results):
            struct = struct_data['structure']
            wavelengths = [res['wavelength'] for res in struct_data['results']]
            R_meep = [res['R_meep'] for res in struct_data['results']]
            T_meep = [res['T_meep'] for res in struct_data['results']]
            R_tmm = [res['R_tmm'] for res in struct_data['results']]
            T_tmm = [res['T_tmm'] for res in struct_data['results']]
            
            # R比较
            plt.subplot(2, 3, i+1)
            plt.plot(wavelengths, R_meep, 'bo-', label='Meep R')
            plt.plot(wavelengths, R_tmm, 'r--', label='TMM R')
            plt.title(f'{struct}结构 - 反射率比较')
            plt.xlabel('波长 (nm)')
            plt.ylabel('反射率')
            plt.legend()
            plt.grid(True)
            
            # T比较
            plt.subplot(2, 3, i+4)
            plt.plot(wavelengths, T_meep, 'go-', label='Meep T')
            plt.plot(wavelengths, T_tmm, 'm--', label='TMM T')
            plt.title(f'{struct}结构 - 透射率比较')
            plt.xlabel('波长 (nm)')
            plt.ylabel('透射率')
            plt.legend()
            plt.grid(True)
        
        plt.tight_layout()
        plt.savefig(f"{self.config.OUTPUT_DIR}/validation_comparison.png", dpi=150)
        plt.close()

# ====================== 材料分布优化器 ======================
class MaterialOptimizer:
    """
    材料分布优化器
    功能：调整材料分布以减少模拟与实测的差异
    
    属性:
        config: 配置对象
        material_db: 材料数据库
        simulator: Meep模拟器
        material_dist: 材料分布
        nn_model: 神经网络模型
        loss_history: 损失历史记录
        
    方法:
        create_loss_matrix: 创建每个区域的损失矩阵
        evaluate_distribution: 评估当前分布的损失
        neural_network_setup: 设置神经网络
        optimize: 主优化循环
    """
    
    def __init__(self, config, material_db, simulator, material_dist):
        self.config = config
        self.material_db = material_db
        self.simulator = simulator
        self.material_dist = material_dist
        self.loss_history = []
        self.neural_network_setup()
    
    def create_loss_matrix(self):
        """
        创建区域损失矩阵
        基于模拟结果与实验数据的差异计算每个区域的损失权重
        
        返回:
            loss_matrix: 每个区域的损失值
        """
        # 简化的损失计算：假设所有区域贡献平均
        num_regions = len(self.material_dist.region_materials)
        loss_matrix = np.ones(num_regions) * 0.5  # 初始损失值
        
        # 更复杂的实现可以根据区域材料类型和位置分配权重
        # 例如：靠近表面的区域影响更大
        for region_id, material in self.material_dist.region_materials.items():
            # 根据位置加权：靠近表面的区域损失权重更大
            min_z, max_z = float('inf'), float('-inf')
            for i in range(self.config.GRID_SIZE[0]):
                for j in range(self.config.GRID_SIZE[1]):
                    for k in range(self.config.GRID_SIZE[2]):
                        if self.material_dist.grid[i, j, k][1] == region_id:
                            if k < min_z:
                                min_z = k
                            if k > max_z:
                                max_z = k
            
            # z位置权重：k值越小（越靠近表面），权重越大
            pos_weight = 1.0 - (min_z / self.config.GRID_SIZE[2])
            loss_matrix[region_id] *= pos_weight
            
        return loss_matrix
    
    def evaluate_distribution(self, wavelength, angle, polarization, R_sim, T_sim):
        """
        评估当前材料分布的质量
        
        参数:
            wavelength: 波长(nm)
            angle: 入射角度(度)
            polarization: 偏振方向
            R_s极im: 模拟反射率
            T_sim: 模拟透射率
            
        返回:
            loss: 综合损失值
        """
        # 获取实验R,T数据
        if polarization == 's':
            R_meas, T_meas = self.material_db.get_interpolated_RT('TiN_4nm', wavelength)
        else: # p偏振
            R_meas, T_meas = self.material_db.get_interpolated_RT('Al2O3', wavelength)
        
        # 计算损失
        if R_meas is None or T_meas is None:
            return float('inf')
        
        loss_R = abs(R_sim - R_meas)
        loss_T = abs(T_sim - T_meas)
        
        # 综合损失
        return 0.7 * loss_R + 0.3 * loss_T
    
    def neural_network_setup(self):
        """
        设置神经网络模型用于预测损失
        
        网络结构:
            输入: (波长, 角度, 偏振, 材料分布特征)
            输出: 预测的损失值
        """
        # 创建简单的神经网络
        self.nn_model = MLPRegressor(
            hidden_layer_sizes=(100, 50, 20),
            activation='relu',
            solver='adam',
            max_iter=1000,
            random_state=42
        )
        
        # 初始虚拟训练数据
        X_train = np.random.rand(10, 5)  # 10个样本，5个特征
        y_train = np.random.rand(10)    # 10个目标值
        self.nn_model.fit(X_train, y_train)
    
    def update_neural_network(self, wavelength, angle, polarization, distribution_features, loss):
        """
        用新数据更新神经网络
        
        参数:
            wavelength: 波长(nm)
            angle: 入射角度(度)
            polarization: 偏振方向
            distribution_features: 材料分布特征向量
            loss: 当前损失值
        """
        # 创建特征向量
        features = np.array([wavelength, angle, 1 if polarization == 's' else 0] + distribution_features)
        
        # 转换为神经网络输入格式
        X = features.reshape(1, -1)
        y = np.array([loss])
        
        # 部分拟合
        self.nn_model.partial_fit(X, y)
    
    def optimize(self):
        """
        主优化循环
        使用贝叶斯优化调整材料分布
        
        流程:
            1. 遍历所有波长、角度和偏振组合
            2. 评估当前材料分布
            3. 创建损失矩阵
            4. 优化材料分布
            5. 保存中间结果
        """
        print("开始材料分布优化...")
        start_time = time.time()
        
        for iteration in range(self.config.MAX_ITER):
            iteration_start = time.time()
            total_loss = 0
            eval_count = 0
            print(f"\n迭代 {iteration+1}/{self.config.MAX_ITER}")
            
            # 准备保存数据
            results = []
            
            for wavelength in tqdm(self.config.WAVELENGTHS, desc="波长模拟"):
                for angle in self.config.ANGLES:
                    for pol in self.config.POLARIZATIONS:
                        # 使用当前分布进行模拟
                        R_sim, T_sim = self.simulator.simulate(
                            self.material_dist, wavelength, pol, angle
                        )
                        
                        # 评估损失
                        loss = self.evaluate_distribution(wavelength, angle, pol, R_sim, T_sim)
                        total_loss += loss
                        eval_count += 1
                        
                        # 保存结果
                        results.append({
                            'wavelength': wavelength,
                            'angle': angle,
                            'polarization': pol,
                            'R_sim': R_sim,
                            'T_sim': T_sim,
                            'loss': loss
                        })
            
            # 计算平均损失
            avg_loss = total_loss / eval_count if eval_count > 0 else float('inf')
            self.loss_history.append(avg_loss)
            
            # 更新材料分布
            loss_matrix = self.create_loss_matrix()
            self.material_dist.optimize_distribution(
                loss_matrix, 
                learning_rate=self.config.LEARNING_RATE,
                mutation_rate=self.config.MUTATION_RATE
            )
            
            # 保存进度
            if (iteration + 1) % self.config.SAVE_INTERVAL == 0:
                self.save_results(iteration, results)
                # 可视化中间分布
                self.material_dist.visualize_slice(
                    50, 'z', 
                    f"{self.config.OUTPUT_DIR}/distribution_iter_{iteration+1}.png"
                )
            
            iteration_time = time.time() - iteration_start
            print(f"迭代 {iteration+1} 完成 - 平均损失: {avg_loss:.6f} - 耗时: {iteration_time:.2f}秒")
        
        total_time = time.time() - start_time
        print(f"优化完成! 总耗时: {total_time:.2f}秒")
        
        # 保存最终结果
        self.save_final_results()
        
        # 可视化最终分布
        self.material_dist.visualize_slice(50, 'z', 
                                          f"{self.config.OUTPUT_DIR}/final_distribution.png")
    
    def save_results(self, iteration, results):
        """保存迭代结果到文件"""
        df = pd.DataFrame(results)
        df.to_csv(f"{self.config.OUTPUT_DIR}/iteration_{iteration+1}_results.csv", index=False)
        
        # 保存损失历史
        loss_df = pd.DataFrame({
            'iteration': range(1, iteration+2),
            'loss': self.loss_history
        })
        loss_df.to_csv(f"{self.config.OUTPUT_DIR}/loss_history.csv", index=False)
        
        # 创建可视化
        plt.figure(figsize=(10, 6))
        plt.plot(loss_df['iteration'], loss_df['loss'], 'o-')
        plt.xlabel('迭代次数')
        plt.ylabel('损失')
        plt.title('优化过程损失变化')
        plt.grid(True)
        plt.save极(f"{self.config.OUTPUT_DIR}/loss_history.png", dpi=150)
        plt.close()
    
    def save_final_results(self):
        """保存最终材料和分布信息"""
        # 保存最终材料分布
        dist_data = []
        for i in range(self.config.GRID_SIZE[0]):
            for j in range(self.config.GRID_SIZE[1]):
                for k in range(self.config.GRID_SIZE[2]):
                    material, region = self.material_dist.grid[i, j, k]
                    dist_data.append({'x': i, 'y': j, 'z': k, 'material': material, 'region': region})
        
        dist_df = pd.DataFrame(dist_data)
        dist_df.to_csv(f"{self.config.OUTPUT_DIR}/final_distribution.csv", index=False)
        
        # 保存最终材料分布统计
        material_counts = defaultdict(int)
        for _, row in dist_df.iterrows():
            material_counts[row['material']] += 1
        
        stats_df = pd.DataFrame({
            'material': list(material_counts.keys()),
            'count': list(material_counts.values()),
            'percentage': [c / len(dist_df) * 100 for c in material_counts.values()]
        })
        stats_df.to_csv(f"{self.config.OUTPUT_DIR}/material_stats.csv", index=False)
        
        # 可视化材料统计
        plt.figure(figsize=(10, 6))
        stats_df.plot(kind='bar', x='material', y='percentage', legend=False)
        plt.ylabel('百分比 (%)')
        plt.title('最终材料分布比例')
        plt.xticks(rotation=45)
        plt.grid(axis='y')
        plt.tight_layout()
        plt.savefig(f"{self.config.OUTPUT_DIR}/material_distribution.png", dpi=150)
        plt.close()

# ====================== 主程序 ======================
def main():
    # 初始化配置
    config = Config()
    
    # 创建材料数据库
    material_db = MaterialDatabase(config)
    
    # 运行验证模块
    validator = ValidationModule(material_db, config)
    validation_passed = validator.run_validation()
    
    if not validation_passed:
        print("验证失败! 请检查设置和材料数据。")
        return
    
    print("验证通过! 开始主优化程序...")
    
    # 创建材料分布
    material_dist = MaterialDistribution(config)
    
    # 创建Meep模拟器
    simulator = MeepSimulator(material_db, config)
    
    # 创建优化器
    optimizer = MaterialOptimizer(config, material_db, simulator, material_dist)
    
    # 可视化初始分布
    material_dist.visualize_slice(50, 'z', 
                                 f"{config.OUTPUT_DIR}/initial_distribution.png")
    
    # 开始优化
    optimizer.optimize()
    
    print("所有结果已保存至:", config.OUTPUT_DIR)

if __name__ == "__main__":
    # 检查Meep安装
    try:
        mp.check_meep()
        print("Meep安装正确，开始计算...")
        main()
    except Exception as e:
        print(f"Meep检查失败: {e}")
        print("请确保已正确安装Meep:")
        print("1. 创建conda环境: conda create -n pymeep python=3.9")
        print("2. 激活环境: conda activate pymeep")
        print("3. 安装PyMeep: conda install -c conda-forge pymeep")
        print("4. 安装其他依赖: conda install numpy pandas matplotlib scipy scikit-learn scikit-optimize networkx")
        print("5. 安装TMM库: pip install tmm")