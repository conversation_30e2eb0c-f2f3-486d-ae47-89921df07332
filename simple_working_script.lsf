# 简单可用版 Lumerical FDTD 脚本
# 移除所有可能有问题的语法，确保兼容性

# ====================== 配置参数 ======================
# 结构参数（单位：米）
px = 300e-9;         # 单胞 x 尺寸 (300 nm)
py = 300e-9;         # 单胞 y 尺寸 (300 nm)

# 层厚度（单位：米）
air_bot = 300e-9;    # 底部空气层 (300 nm)
t_au_bot = 80e-9;    # 底部金层 (80 nm)
t_sio2_bot = 150e-9; # 底部二氧化硅层 (150 nm)
t_nbtiin = 19e-9;    # NbTiN 层 (19 nm)
t_sio2_top = 100e-9; # 顶部二氧化硅层 (100 nm)
air_top = 300e-9;    # 顶部空气层 (300 nm)

# Au 小方块参数（单位：米）
patch_w = 80e-9;     # 宽度 (80 nm)
patch_l = 80e-9;     # 长度 (80 nm)
patch_t = 20e-9;     # 厚度 (20 nm)
patch_gap = 5e-9;    # 离上表面距离 (5 nm)

# 仿真参数
lam_start = 800e-9;  # 起始波长 (800 nm)
lam_stop = 2000e-9;  # 结束波长 (2000 nm)
sim_time = 2000e-15; # 仿真时间 (fs)
mesh_acc = 3;        # 网格精度

# ====================== 初始化 ======================
?"开始 Lumerical FDTD 仿真...";
switchtolayout;
deleteall;
redrawoff;

# ====================== 跳过材料创建 ======================
?"跳过材料创建，使用现有材料...";

# ====================== 设置 FDTD 区域 ======================
?"设置 FDTD 仿真区域...";

# 计算总厚度
lz = air_bot + t_au_bot + t_sio2_bot + t_nbtiin + t_sio2_top + air_top;
z0 = -lz/2;

# 添加 FDTD 区域
addfdtd;
set("dimension", "3D");
set("x span", px);
set("y span", py);
set("z span", lz);
set("mesh accuracy", mesh_acc);

# 设置边界条件
set("x min bc", "Periodic");
set("x max bc", "Periodic");
set("y min bc", "Periodic");
set("y max bc", "Periodic");
set("z min bc", "PML");
set("z max bc", "PML");

# 设置波长和时间
set("wavelength start", lam_start);
set("wavelength stop", lam_stop);
set("simulation time", sim_time);

?"FDTD 区域设置完成";

# ====================== 添加光源和监视器 ======================
?"添加光源和监视器...";

# 计算关键位置
z_sio2_top = z0 + air_bot + t_au_bot + t_sio2_bot + t_nbtiin + t_sio2_top;
src_z = z0 + lz*0.5 - 0.1*air_top;
refl_z = z_sio2_top + 0.10*air_top;
trans_z = z0 + 0.10*air_bot;

# 添加平面波光源
addplane;
set("name", "src");
set("injection axis", "z");
set("direction", "Backward");
set("x span", px);
set("y span", py);
set("z", src_z);
set("wavelength start", lam_start);
set("wavelength stop", lam_stop);

# 添加反射功率监视器
addpower;
set("name", "R_monitor");
set("monitor type", "2D Z-normal");
set("x span", px);
set("y span", py);
set("z", refl_z);
set("override global monitor settings", 1);
set("use source limits", 1);

# 添加透射功率监视器
addpower;
set("name", "T_monitor");
set("monitor type", "2D Z-normal");
set("x span", px);
set("y span", py);
set("z", trans_z);
set("override global monitor settings", 1);
set("use source limits", 1);

?"光源和监视器添加完成";

# ====================== 第一次仿真：仅光源 ======================
?"运行基准仿真（仅光源）...";
run;

# 获取基准数据
R_ref_result = getresult("R_monitor", "power");
T_ref_result = getresult("T_monitor", "power");
lam_R_ref = R_ref_result.lambda;
P_R_ref = R_ref_result.power;
lam_T_ref = T_ref_result.lambda;
P_T_ref = T_ref_result.power;

?"基准仿真完成，数据点数: " + num2str(length(lam_R_ref));

# ====================== 构建结构 ======================
?"构建多层结构...";

# 自下而上构建层结构
current_z = z0;

# 底部空气层
z_center = current_z + air_bot * 0.5;
addrect;
set("name", "air_bottom");
set("material", "Air");
set("x span", px);
set("y span", py);
set("z", z_center);
set("z span", air_bot);
current_z = current_z + air_bot;

# 底部金层
z_center = current_z + t_au_bot * 0.5;
addrect;
set("name", "Au_bottom");
set("material", "Au (Gold) - Johnson and Christy");
set("x span", px);
set("y span", py);
set("z", z_center);
set("z span", t_au_bot);
current_z = current_z + t_au_bot;

# 底部二氧化硅层
z_center = current_z + t_sio2_bot * 0.5;
addrect;
set("name", "SiO2_bottom");
set("material", "SiO2 (Glass) - Palik");
set("x span", px);
set("y span", py);
set("z", z_center);
set("z span", t_sio2_bot);
current_z = current_z + t_sio2_bot;

# NbTiN 层 - 使用硅材料替代
z_center = current_z + t_nbtiin * 0.5;
addrect;
set("name", "NbTiN_layer");
set("material", "Si (Silicon) - Palik");
set("x span", px);
set("y span", py);
set("z", z_center);
set("z span", t_nbtiin);
current_z = current_z + t_nbtiin;

# 顶部二氧化硅层
z_center = current_z + t_sio2_top * 0.5;
addrect;
set("name", "SiO2_top");
set("material", "SiO2 (Glass) - Palik");
set("x span", px);
set("y span", py);
set("z", z_center);
set("z span", t_sio2_top);
z_sio2_top_surface = current_z + t_sio2_top;
current_z = current_z + t_sio2_top;

# 顶部空气层
z_center = current_z + air_top * 0.5;
addrect;
set("name", "air_top");
set("material", "Air");
set("x span", px);
set("y span", py);
set("z", z_center);
set("z span", air_top);

# 添加 Au 小方块
patch_z = z_sio2_top_surface + patch_t*0.5 + patch_gap;

# 固定位置（避免随机函数可能的问题）
patch_x = 0;
patch_y = 0;

addrect;
set("name", "Au_patch");
set("material", "Au (Gold) - Johnson and Christy");
set("x", patch_x);
set("x span", patch_w);
set("y", patch_y);
set("y span", patch_l);
set("z", patch_z);
set("z span", patch_t);

?"结构构建完成";

# ====================== 第二次仿真：带结构 ======================
?"运行结构仿真...";
run;

# 获取结构数据
R_struct_result = getresult("R_monitor", "power");
T_struct_result = getresult("T_monitor", "power");
lam_R = R_struct_result.lambda;
P_R_struct = R_struct_result.power;
lam_T = T_struct_result.lambda;
P_T_struct = T_struct_result.power;

# 计算源功率
P_source = sourcepower("src");

# 计算反射率和透射率
R_data = P_R_struct / P_source;
T_data = P_T_struct / P_source;

?"结构仿真完成，数据点数: " + num2str(length(lam_R));

# ====================== 保存结果 ======================
?"保存仿真结果...";

# 保存结构数据
output_file = "RT_structure_results.txt";
data_matrix = [lam_R*1e9, R_data, T_data];
write(output_file, "wavelength_nm,R,T");
write(output_file, data_matrix, "a");
?"结构数据已保存: " + output_file;

# ====================== 输出关键结果 ======================
?"";
?"========================================";
?"关键波长的仿真结果:";
?"========================================";

# 1000 nm 结果
lambda_1000 = 1000e-9;
R_1000 = interp(lam_R, R_data, lambda_1000);
T_1000 = interp(lam_T, T_data, lambda_1000);
?"λ = 1000 nm:  R = " + num2str(R_1000, "%.4f") + ",  T = " + num2str(T_1000, "%.4f") + ",  (R+T = " + num2str(R_1000+T_1000, "%.4f") + ")";

# 1500 nm 结果
lambda_1500 = 1500e-9;
R_1500 = interp(lam_R, R_data, lambda_1500);
T_1500 = interp(lam_T, T_data, lambda_1500);
?"λ = 1500 nm:  R = " + num2str(R_1500, "%.4f") + ",  T = " + num2str(T_1500, "%.4f") + ",  (R+T = " + num2str(R_1500+T_1500, "%.4f") + ")";

?"========================================";

# ====================== 完成 ======================
redrawon;
?"";
?"仿真完成！";
?"结果文件: " + output_file;
?"";
?"注意：此版本使用了简化的 NbTiN 材料模型";
?"如需使用精确的材料数据，请手动在材料库中创建 NbTiN 材料";
