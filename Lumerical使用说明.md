# Lumerical FDTD 脚本使用说明

## 快速开始

### 1. 准备材料数据文件

创建一个 CSV 文件，包含 NbTiN 的光学常数数据，格式如下：

```csv
wavelength(nm),n,k
400,3.2,2.8
500,3.0,2.6
600,2.8,2.4
...
```

**注意：**
- 第一行必须是列名：`wavelength(nm),n,k`
- 波长单位是纳米 (nm)
- n 是折射率，k 是消光系数
- 数据点越多，仿真精度越高

### 2. 在 Lumerical 中运行脚本

#### 方法一：使用简化脚本（推荐）

1. 打开 Lumerical FDTD
2. 打开 Script Editor (菜单：View → Script Editor)
3. 打开文件 `simple_lumerical_script.lsf`
4. 修改第 10 行的文件路径：
   ```
   NK_FILE = "C:/Users/<USER>/Desktop/NbTiN_nk.csv";
   ```
5. 点击运行按钮 (▶️) 或按 F5

#### 方法二：使用完整脚本

1. 打开 `lumerical_internal_script.lsf`
2. 同样修改材料文件路径
3. 运行脚本

### 3. 查看结果

脚本运行完成后会：

1. **在控制台显示关键结果：**
   ```
   ========================================
   关键波长的仿真结果:
   ========================================
   λ = 1000 nm:  R = 0.3245,  T = 0.5678,  (R+T = 0.8923)
   λ = 1500 nm:  R = 0.2134,  T = 0.6789,  (R+T = 0.8923)
   ========================================
   ```

2. **生成结果文件：**
   - `RT_results.txt`: 包含完整的波长-反射率-透射率数据

3. **在 Lumerical 中显示结构：**
   - 可以在 3D 视图中查看构建的多层结构

## 结构说明

脚本会自动构建以下多层结构（从下到上）：

```
┌─────────────────────────────────┐
│        Air (300 nm)             │  ← 顶部空气层
│  ┌─ Au patch (80×80×20 nm)      │  ← Au 小方块
├─────────────────────────────────┤
│      SiO2 (100 nm)              │  ← 顶部二氧化硅层
├─────────────────────────────────┤
│      NbTiN (19 nm)              │  ← NbTiN 功能层
├─────────────────────────────────┤
│      SiO2 (150 nm)              │  ← 底部二氧化硅层
├─────────────────────────────────┤
│      Au (80 nm)                 │  ← 底部金层
├─────────────────────────────────┤
│        Air (300 nm)             │  ← 底部空气层
└─────────────────────────────────┘
```

**边界条件：**
- x, y 方向：周期性边界条件（模拟无限阵列）
- z 方向：PML 吸收边界条件
- 单胞尺寸：300×300 nm

## 参数调整

如果需要修改结构参数，可以编辑脚本中的配置部分：

```lsf
# 结构参数
px = 0.3e-6;      # 单胞 x 尺寸 (300 nm)
py = 0.3e-6;      # 单胞 y 尺寸 (300 nm)
air_bot = 0.3e-6;    # 底部空气 (300 nm)
au_bot = 0.08e-6;    # 底部金层 (80 nm)
sio2_bot = 0.15e-6;  # 底部 SiO2 (150 nm)
nbtiin = 0.019e-6;   # NbTiN 层 (19 nm)
sio2_top = 0.1e-6;   # 顶部 SiO2 (100 nm)
air_top = 0.3e-6;    # 顶部空气 (300 nm)

# Au 小方块
patch_size = 0.08e-6;  # 80 nm
patch_thick = 0.02e-6; # 20 nm

# 仿真参数
lambda_min = 800e-9;   # 800 nm
lambda_max = 2000e-9;  # 2000 nm
```

## 常见问题

### 1. 材料导入失败
**错误信息：** "错误：找不到材料文件"

**解决方法：**
- 检查文件路径是否正确
- 确保使用正斜杠 `/` 而不是反斜杠 `\`
- 确保文件存在且可读

### 2. CSV 格式错误
**错误信息：** 材料导入时出错

**解决方法：**
- 确保第一行是 `wavelength(nm),n,k`
- 检查数据中是否有空行或非数字字符
- 使用 UTF-8 编码保存 CSV 文件

### 3. 仿真不收敛
**现象：** 仿真运行很长时间或结果异常

**解决方法：**
- 增加仿真时间（修改 `set("simulation time", 3000e-15);`）
- 降低网格精度（修改 `set("mesh accuracy", 2);`）
- 检查结构几何是否合理

### 4. 内存不足
**现象：** 仿真启动失败或崩溃

**解决方法：**
- 降低网格精度
- 减小仿真区域
- 关闭其他程序释放内存

## 结果分析

### 理解输出结果

- **R (反射率)**: 反射功率与入射功率的比值
- **T (透射率)**: 透射功率与入射功率的比值
- **R+T**: 应该接近但小于 1（由于吸收损耗）

### 典型结果范围

对于金属-介质多层结构：
- 反射率通常在 0.1-0.8 之间
- 透射率通常在 0.1-0.6 之间
- 总和 R+T 通常在 0.6-0.9 之间（其余为吸收）

### 结果验证

1. **物理合理性检查：**
   - R, T 值应该在 0-1 之间
   - R+T 应该 ≤ 1
   - 光谱应该平滑连续

2. **收敛性检查：**
   - 增加仿真时间，结果应该稳定
   - 提高网格精度，结果变化应该很小

## 进阶功能

### 1. 参数扫描

可以修改脚本进行参数扫描，例如扫描 NbTiN 厚度：

```lsf
for (thickness = 10e-9; thickness <= 30e-9; thickness = thickness + 5e-9) {
    # 修改 NbTiN 厚度
    nbtiin = thickness;
    # 重新构建结构并运行仿真
    # ...
}
```

### 2. 添加更多监视器

可以添加场分布监视器来查看电磁场：

```lsf
addprofile;
set("name", "field_monitor");
set("monitor type", "2D Z-normal");
set("z", z_nbtiin);  # NbTiN 层中心
```

### 3. 偏振分析

可以设置不同的偏振方向：

```lsf
# TE 偏振 (s 偏振)
set("polarization angle", 0);

# TM 偏振 (p 偏振)  
set("polarization angle", 90);
```

## 技术支持

如果遇到问题：

1. 检查 Lumerical 版本兼容性
2. 查看 Lumerical 官方文档
3. 检查脚本语法是否正确
4. 验证材料数据格式

## 版本信息

- 脚本版本：2.0
- 兼容 Lumerical FDTD 2020a 及以上版本
- 测试环境：Windows 10, Lumerical 2023a
