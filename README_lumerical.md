# Lumerical FDTD 自动化仿真脚本使用说明

## 概述

这是一个改进版的 Lumerical FDTD 自动化仿真脚本，用于计算多层光学结构的反射率和透射率。

## 主要改进

### 1. 代码结构优化
- **面向对象设计**: 使用 `LumericalSimulation` 类组织代码
- **配置类**: `SimulationConfig` 类集中管理所有参数
- **类型提示**: 添加完整的类型注解，提高代码可读性
- **错误处理**: 完善的异常处理和日志记录

### 2. 功能增强
- **智能材料数据读取**: 自动识别多种列名格式
- **路径自动检测**: 自动查找 Lumerical 安装路径
- **详细日志**: 完整的仿真过程记录
- **数据验证**: 输入数据的有效性检查

### 3. 易用性提升
- **清晰的配置**: 所有参数集中在配置类中
- **详细文档**: 完整的函数和类文档
- **错误提示**: 友好的错误信息和解决建议

## 使用方法

### 1. 配置参数

编辑 `SimulationConfig` 类中的参数：

```python
class SimulationConfig:
    def __init__(self):
        # 材料数据文件路径
        self.NK_FILE = r"C:\Users\<USER>\Desktop\NbTiN_nk.xlsx"
        
        # 结构参数（单位：μm）
        self.UNIT_CELL_X = 0.300  # 单胞 x 尺寸
        self.UNIT_CELL_Y = 0.300  # 单胞 y 尺寸
        
        # 层厚度（单位：μm）
        self.AIR_BOTTOM = 0.300    # 底部空气层
        self.AU_BOTTOM = 0.080     # 底部金层
        self.SIO2_BOTTOM = 0.150   # 底部二氧化硅层
        self.NBTIN = 0.019         # NbTiN 层
        self.SIO2_TOP = 0.100      # 顶部二氧化硅层
        self.AIR_TOP = 0.300       # 顶部空气层
        
        # Au 小方块参数
        self.AU_PATCH_WIDTH = 0.080      # 宽度
        self.AU_PATCH_LENGTH = 0.080     # 长度
        self.AU_PATCH_THICKNESS = 0.020  # 厚度
        self.AU_PATCH_GAP = 0.005        # 离上表面距离
        
        # 仿真参数
        self.WAVELENGTH_START = 0.80  # 起始波长 (μm)
        self.WAVELENGTH_STOP = 2.00   # 结束波长 (μm)
        self.SIMULATION_TIME = 2000e-15  # 仿真时间 (fs)
        self.MESH_ACCURACY = 3        # 网格精度
        
        # 输出参数
        self.REPORT_WAVELENGTHS = [1.000, 1.500]  # 报告的关键波长 (μm)
```

### 2. 准备材料数据

材料数据文件支持以下格式：

#### Excel 文件 (.xlsx/.xls)
- 列名可以是：`wavelength(nm)`, `wavelength`, `lambda(nm)`, `λ(nm)`, `nm`, `wl`
- n 值列名：`n`, `real(n)`, `real`, `n_real`
- k 值列名：`k`, `imag(k)`, `imag`, `k_imag`

#### CSV 文件 (.csv)
```csv
wavelength(nm),n,k
400,2.1,0.5
500,2.0,0.4
...
```

### 3. 运行脚本

```bash
python 20250922lumerical.py
```

### 4. 输出结果

脚本会生成以下文件：
- `NbTiN_data.csv`: 材料光学常数数据
- `RT_reference.csv`: 基准仿真结果（仅光源）
- `RT_structure.csv`: 结构仿真结果
- `lumerical_simulation.log`: 详细日志文件

## 仿真流程

1. **初始化**: 设置 FDTD 环境和单位
2. **材料导入**: 读取并导入 NbTiN 材料数据
3. **结构设置**: 配置仿真区域和边界条件
4. **光源和监视器**: 添加平面波光源和功率监视器
5. **基准仿真**: 运行仅光源的仿真（归一化）
6. **结构仿真**: 添加多层结构并运行仿真
7. **数据处理**: 提取并保存反射率和透射率数据
8. **结果报告**: 输出关键波长的 R/T 值

## 结构说明

仿真的多层结构（从下到上）：
```
Air (300 nm)           ← 底部空气层
Au (80 nm)             ← 底部金层  
SiO2 (150 nm)          ← 底部二氧化硅层
NbTiN (19 nm)          ← NbTiN 功能层
SiO2 (100 nm)          ← 顶部二氧化硅层
Air (300 nm)           ← 顶部空气层
  └─ Au patch (80×80×20 nm)  ← Au 小方块
```

## 边界条件

- **x, y 方向**: 周期性边界条件（模拟无限周期阵列）
- **z 方向**: PML 吸收边界条件
- **单胞尺寸**: 300×300 nm

## 注意事项

1. **Lumerical 安装**: 确保 Lumerical FDTD 已正确安装
2. **Python 环境**: 建议使用 Lumerical 自带的 Python 环境
3. **内存要求**: 3D 仿真需要足够的内存
4. **仿真时间**: 根据结构复杂度，仿真可能需要几分钟到几小时

## 故障排除

### 常见问题

1. **无法导入 lumapi**
   - 检查 Lumerical 安装路径
   - 确认 Python 环境正确

2. **材料数据读取失败**
   - 检查文件路径是否正确
   - 确认列名格式是否支持

3. **仿真收敛问题**
   - 增加仿真时间
   - 调整网格精度
   - 检查结构几何是否合理

### 日志文件

查看 `lumerical_simulation.log` 文件获取详细的错误信息和仿真过程记录。

## 扩展功能

脚本设计为模块化，可以轻松扩展：

- 添加新的材料
- 修改结构几何
- 增加新的监视器
- 实现参数扫描
- 添加后处理功能

## 版本历史

- **v2.0**: 完全重构，面向对象设计，增强错误处理
- **v1.0**: 原始脚本版本
